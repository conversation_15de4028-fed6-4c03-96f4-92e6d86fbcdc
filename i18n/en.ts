const en = {
  login: {
    button: 'Login',
    header: 'Welcome Back!',
  },
  dashboard: {
    timeAtWork: 'Time at Work',
  },
  formFill: {
    fnamePlaceholder: 'Please fill your name',
    lnamePlaceholder: 'Please fill your last name',
    idCardPlaceholder: 'Please fill your ID card'
  },
  // Consent Page
  consent: {
    headers: "ข้อกำหนดและเงื่อนไขของบริการ",
    subHeader: "Samitivej Virtual Hospital",
    lblCheckbox: "ฉันยอมรับข้อกำหนดและเงื่อนไข",
    btnNext: "ถัดไป",
  },
  register: {
    errorFirstName: "Please fill your name",
    errorLastName: "Please fill your last name",
    errorTel: "Please fill your mobile number",
    errorFormatTel: "Please fill your mobile number in correct format",
    title: "Register to use the system",
    subTitle: "Please use the latest version of your browser. If you are still unable to access, please contact 02-022-2222",
    firstNameTitle: "First Name *",
    lastNameTitle: "Last Name *",
    telTitle: "Mobile Number *",
    firstNamePlaceholder: "Please fill your name",
    lastNamePlaceholder: "Please fill your last name",
    telPlaceholder: "Please fill your mobile number",
    btnSubmit: "Register",
  },
  preCall: {
    waitingStatusMessage: "Waiting for the next available doctor...",
    backButton: "Back",
    cancelCallButton: "Cancel Call",
    mainHeading: "ขออภัยในความไม่สะดวก",
    contentHeader: "ขออภัยในความไม่สะดวก",
    contentText: "ท่านสามารถทำการฝากข้อความเพื่อให้ติดต่อกลับ หรือ รอสายเพื่อรับบริการ",
    waitForServiceButton: "รอสายเพื่อรับบริการ",
    leaveMessageButton: "ฝากข้อความ",
  },
  leaveMessage: {
    header: "กรุณาระบุอาการเบื้องต้นของท่านเพื่อให้แพทย์สามารถให้คำแนะนำได้อย่างถูกต้อง",
    firstNameTitle: "ชื่อ",
    lastNameTitle: "นามสกุล",
    telTitle: "เบอร์โทรศัพท์",
    emailTitle: "อีเมล (ถ้ามี)",
    initialSymptomsTitle: "ระบุอาการเบื้องต้น",
    btnSubmit: "ส่งข้อความ",
    btnBack: "ย้อนกลับ",
  },
  inCall: {
    backButton: "Back",
    endCallButton: "End Call",
    peopleHeading: "People",
    chatHeading: "Chat",
    micLabel: "Mic",
    cameraLabel: "Camera",
    raiseHandLabel: "Raise",
    presentScreenLabel: "Present",
    peopleLabel: "People",
    chatLabel: "Chat",
  },
  confirm: {
    header: "ขั้นตอนการรับบริการ",
    subHeader: "Samitivej Virtual Hospital",
    btnConfirm: "เริ่มสนทนา",
  },
  startCall: {
    startCallButton: "Start call",
    callButton: "เริ่มสนทนา",
    startCallText: "Start a call",
  },
};

export default en;