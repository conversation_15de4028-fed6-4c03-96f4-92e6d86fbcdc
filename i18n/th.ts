const th = {
  login: {
    button: "เข้าสู่ระบบ",
    header: "ยินดีต้อนรับกลับ!",
  },
  dashboard: {
    timeAtWork: "เวลาที่ทำงาน",
  },
  formFill: {
    fnamePlaceholder: "กรุณากรอกชื่อ",
    lnamePlaceholder: "กรุณากรอกนามสกุล",
    idCardPlaceholder: "กรุณากรอกบัตรประชาชน",
  },
  // Consent Page
  consent: {
    headers: "ข้อกำหนดและเงื่อนไขของบริการ",
    subHeader: "Samitivej Virtual Hospital",
    lblCheckbox: "ฉันยอมรับข้อกำหนดและเงื่อนไข",
    btnNext: "ถัดไป",
  },
  register: {
    errorFirstName: "กรุณากรอกชื่อ",
    errorLastName: "กรุณากรอกนามสกุล",
    errorTel: "กรุณากรอกเบอร์โทรศัพท์",
    errorFormatTel: "รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง",
    title: "ลงทะเบียนเพื่อใช้งานระบบ",
    subTitle: "เพื่อการใช้งานที่ดีที่สุด กรุณาอัพเดทบราวเซอร์ของท่านให้เป็นเวอร์ชั่นล่าสุด หากเข้าใช้งานไม่ได้ติดต่อ 02-022-2222",
    firstNameTitle: "ชื่อ *",
    lastNameTitle: "นามสกุล *",
    telTitle: "เบอร์โทรศัพท์ *",
    firstNamePlaceholder: "กรุณากรอกชื่อ",
    lastNamePlaceholder: "กรุณากรอกนามสกุล",
    telPlaceholder: "กรุณากรอกเบอร์โทรศัพท์",
    btnSubmit: "ลงทะเบียน"
  },
  preCall: {
    waitingStatusMessage: "กรุณารอสักครู่ค่ะ…ท่านกำลังเป็นคิวถัดไป",
    backButton: "ย้อนกลับ",
    cancelCallButton: "ยกเลิกการสนทนา",
    mainHeading: "ขออภัยในความไม่สะดวก",
    contentHeader: "ขณะนี้พยาบาลกำลังให้บริการคนไข้",
    contentText: "ท่านสามารถทำการฝากข้อความเพื่อให้ติดต่อกลับ หรือ รอสายเพื่อรับบริการ",
    waitForServiceButton: "รอสายเพื่อรับบริการ",
    leaveMessageButton: "ฝากข้อความ",
  },
  leaveMessage: {
    header: "กรุณาระบุอาการเบื้องต้นของท่านเพื่อให้แพทย์สามารถให้คำแนะนำได้อย่างถูกต้อง",
    firstNameTitle: "ชื่อ",
    lastNameTitle: "นามสกุล",
    telTitle: "เบอร์โทรศัพท์",
    emailTitle: "อีเมล (ถ้ามี)",
    initialSymptomsTitle: "ระบุอาการเบื้องต้น",
    btnSubmit: "ส่งข้อความ",
    btnBack: "ย้อนกลับ",
  },
  inCall: {
    backButton: "ย้อนกลับ",
    endCallButton: "จบการสนทนา",
    peopleHeading: "People",
    chatHeading: "Chat",
    micLabel: "Mic",
    cameraLabel: "Camera",
    raiseHandLabel: "Raise",
    presentScreenLabel: "Present",
    peopleLabel: "People",
    chatLabel: "Chat",
  },
  confirm: {
    header: "ขั้นตอนการรับบริการ",
    subHeader: "Samitivej Virtual Hospital",
    btnConfirm: "เริ่มสนทนา",
  },
  startCall: {
    startCallButton: "Start call",
    callButton: "เริ่มสนทนา",
    startCallText: "Start a call",
  },
};

export default th;
