import { error } from "console";

const leaveMessage = {
  header: `//span[@class='mb-4 text-center']`,

  inputInitialSymptoms: `//div[@class='ql-editor ql-blank']`,
  inputEmail: `//input[@name='email']`,
  btnSubmit: `//button[contains(text(),'ส่งข้อความ')]`,
  btnBack: `//button[contains(text(),'ย้อนกลับ')]`,

  titleFirstName: `//p[contains(text(),'ชื่อ')]`,
  titleLastName: `//p[contains(text(),'นามสกุล')]`,
  titleTel: `//p[contains(text(),'เบอร์โทรศัพท์')]`,
  titleEmail: `//p[contains(text(),'อีเมล (ถ้ามี)')]`,
  titleInitialSymptoms: `//p[contains(text(),'ระบุอาการเบื้องต้น')]`,

  inputFirstName: `//input[@name='firstName']`,
  inputLastName: `//input[@name='lastName']`,
  inputTel: `//input[@name='tel']`,

  errorEmail: `//p[contains(text(),'รูปแบบอีเมลไม่ถูกต้อง')]`
} as const;

export default leaveMessage;