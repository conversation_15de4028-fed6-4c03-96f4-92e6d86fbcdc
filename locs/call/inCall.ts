import i18n from '../../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';
const t = i18n[lang];

const inCall = {
  backButton: `//button[contains(text(),'${t.inCall.backButton}')]`,
  endCallButton: `//i[@data-icon-name='ControlButtonEndCall']`,
  microphoneOffButton: `//i[@data-icon-name="ControlButtonMicOff"]`,
  microphoneOnButton: `//i[@data-icon-name="ControlButtonMicOn"]`,
  cameraOffButton: `//i[@data-icon-name="ControlButtonCameraOff"]`,
  cameraOnButton: `//i[@data-icon-name="ControlButtonCameraOn"]`,
  raiseHandButton: `//i[@data-icon-name="ControlButtonRaiseHand"]`,
  lowerHandButton: `//i[@data-icon-name="ControlButtonLowerHand"]`,
  presentScreenOnButton: `//i[@data-icon-name='ControlButtonScreenShareStart']`,
  presentScreenOffButton: `//i[@data-icon-name='ControlButtonScreenShareStop']`,
  leaveCallButton: `//button//span[contains(text(),'Leave')]`,
  skipButton: `//button//span[contains(text(),'Skip')]`,
  peopleButton: `//i[@data-icon-name='ControlBarPeopleButton']`,
  chatButton: `//i[@data-icon-name='ControlBarChatButtonInactive']`,

  peopleHeading: `//div[@aria-label='People']`,
  iconClosePanel: `//i[@data-icon-name="cancel"]`,

  chatHeading: `//div[@aria-label='Chat']`,
  cameraButton: `//button[@data-ui-id='call-composite-camera-button']`,
  inputChat: `//textarea[@id='sendbox']`,
  iconSendBoxAttachFile: `//i[@data-icon-name='SendBoxAttachFile']`,
  iconAcionMsg: `//div[@data-ui-id='chat-composite-message-action-icon']`,
  deleteMessageButton: `//i[@data-icon-name="MessageRemove"]`,
  editMessageButton: `//i[@data-icon-name="MessageEdit"]`,
  textareaEditMessage: `//textarea[@data-ui-id='edit-box']`,
  submitEditMessageButton: `//i[@data-icon-name="EditBoxSubmit"]`,
  userAvatar: `(//div[@role='presentation'])[1]`,

  micLabel: `//span[contains(text(),'Mic')]`,
  cameraLabel: `//span[contains(text(),'Camera')]`,
  raiseHandLabel: `//span[contains(text(),'Raise')]`,
  presentScreenLabel: `//span[contains(text(),'Present')]`,
  peopleLabel: `//span[contains(text(),'People')]`,
  chatLabel: `//span[contains(text(),'Chat')]`,
  dateTimeLabel: `//span[@data-ui-id="message-timestamp"]`

  //span[contains(text(),'Mic')]
} as const;

export default inCall;

// data-ui-id="call-composite-screenshare-button"
// id="screenShareButtonLabel-tooltip"