import i18n from '../../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';
const t = i18n[lang];

const preCall = {
  backButton: `//button[contains(text(),'${t.preCall.backButton}')]`,
  cancelCallButton: `//button[contains(text(),'${t.preCall.cancelCallButton}')]`,
  dialogCancelCall: `//div[@id='pr_id_1']`,

  // Waiting status message
  waitingStatusMessage: `//span[contains(text(),'${t.preCall.waitingStatusMessage}')]`,
  waitingStatusMessageAnimation: `//span[@class='rli-d-i-b lifeline-indicator']//*[name()='svg']`,

  // Confirmation pop-up for cancellation
  mainHeading: `//div[@class='w-full text-center font-semibold text-xl sm:text-2xl']`,
  contentHeader: `//span[contains(text(),'${t.preCall.contentHeader}')]`,
  contentText: `//span[contains(text(),'${t.preCall.contentText}')]`,
  waitForServiceButton: `//button[contains(text(),'${t.preCall.waitForServiceButton}')]`,
  leaveMessageButton: `//button[contains(text(),'${t.preCall.leaveMessageButton}')]`,
  startCallButton: `//button//*//span[contains(text(),'Start call')]`,
  microphoneButton: `//button[@data-ui-id='call-composite-microphone-button']`,
} as const;

export default preCall;