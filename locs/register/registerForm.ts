const registerForm = {
  firstNameInput: `//input[@name="firstName"]`,
  lastNameInput: `//input[@name="lastName"]`,
  telInput: `//input[@name="tel"]`,
  submitButton: `//button[contains(text(),'ลงทะเบียน')]`,
  errorFirstName: `//p[contains(text(), 'กรุณากรอกชื่อ')]`,
  errorLastName: `//p[contains(text(), 'กรุณากรอกนามสกุล')]`,
  errorTel: `//p[contains(text(), 'กรุณากรอกเบอร์โทรศัพท์')]`,
  errorFormatTel: `//p[contains(text(), 'รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง')]`,

  // Header form register
  title: `//h2[contains(text(), 'ลงทะเบียนเพื่อใช้งานระบบ')]`,
  subTitle: `//span[contains(text(), 'เพื่อการใช้งานที่ดีที่สุด กรุณาอัพเดทบราวเซอร์ของท่านให้เป็นเวอร์ชั่นล่าสุด หากเข้าใช้งานไม่ได้ติดต่อ 02-022-2222')]`,

  // title input form
  firstNameTitle: `//p[contains(normalize-space(), 'ชื่อ *')]`,
  lastNameTitle: `//p[contains(normalize-space(), 'นามสกุล *')]`,
  telTitle: `//p[contains(normalize-space(), 'เบอร์โทรศัพท์ *')]`,

  // Placeholder
  firstNamePlaceholder: `//input[@name="firstName"]`,
  lastNamePlaceholder: `//input[@name="lastName"]`,
  telPlaceholder: `//input[@name="tel"]`,
} as const;

export default registerForm;