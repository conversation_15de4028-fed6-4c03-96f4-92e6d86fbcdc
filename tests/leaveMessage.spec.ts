import { test, expect, type Page, } from '@playwright/test';
import { RegisterPage } from '../pages/RegisterPage';
import { ConsentPage } from '../pages/ConsentPage';
import { ConfirmPage } from '../pages/ConfirmPage';
import { DashboardPage } from '../pages/DashboardPage';
import { LoginBackofficePage } from '../pages/LoginBackofficePage';
import { LeaveMessagePage } from '../pages/LeaveMessagePage';
import data from '../data-test/register.json';
import locs from '../locs/leavMessage/index';
import locsCall from '../locs/call/index';
import i18n, { Lang } from '../i18n';

let page: Page;
let consentPage: ConsentPage;
let registerPage: RegisterPage;
let confirmPage: ConfirmPage;
let adminPage: Page;
let statusCall: boolean = false;
const lang = (process.env.LANG === 'th') ? 'th' : 'en';
const t = i18n[lang];

test.beforeEach(async ({ context }) => {
  page = await context.newPage();
  consentPage = new ConsentPage(page);
  registerPage = new RegisterPage(page);
  confirmPage = new ConfirmPage(page);
  await page.goto(process.env.URL_USER!);
  await consentPage.acceptConsent();

  const firstName = data.userAccount[1].personalInfo.firstName;
  const lastName = data.userAccount[1].personalInfo.lastName;
  const tel = data.userAccount[1].contact.phoneNumber;
  await registerPage.submitRegister(firstName, lastName, tel);
  await confirmPage.isClickConfirm();
  // await page.waitForTimeout(3000);
  // statusCall = true;
});

test.afterEach(async ({context}) => {
  if(statusCall === true){
    adminPage = await context.newPage();
    const loginBackofficePage = new LoginBackofficePage(adminPage);
    const dashboardPage = new DashboardPage(adminPage);
    await adminPage.goto(process.env.URL_ADMIN!);
    await loginBackofficePage.loginBackoffice('adminuat', 'temp1234*');
    await dashboardPage.isClickDashboard();
    await dashboardPage.isClickNurseRoom();
    await dashboardPage.isClickInfoButton(data.userAccount[1].personalInfo.fullName);
    await dashboardPage.isEndCallQueue();
    await adminPage.waitForTimeout(1000);
    await dashboardPage.isConfirmEndCallQueue();
    await adminPage.waitForTimeout(1000);
    await dashboardPage.isClickOkButton();
    await page.close();
    statusCall = false;
  } else {
    await page.close();
  }
});

// Api connextion prod send message sucess in prod
test.skip('TC_LMS_SVH_001 : Verify that a message is successfully submitted when all mandatory fields contain valid input.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  await leaveMessagePage.page.fill(locs.inputEmail, '<EMAIL>');
  await leaveMessagePage.page.fill(locs.inputInitialSymptoms, 'Slight fever and sore throat');
  await leaveMessagePage.page.click(locs.btnSubmit);
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('review');
});

// Api connextion prod send message sucess in prod
test.skip('TC_LMS_SVH_002 : Verify that a message is successfully submitted when all mandatory fields contain valid input without entering an email.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  await leaveMessagePage.page.fill(locs.inputInitialSymptoms, 'Slight fever and sore throat');
  await leaveMessagePage.page.click(locs.btnSubmit);
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('review');
});

test('TC_LMS_SVH_003 : Verify that the form prevents submission if the required "Initial Symptoms" field is empty.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  const btnSubmit = page.locator(locs.btnSubmit);
  await expect(btnSubmit).toBeDisabled();
});

test('TC_LMS_SVH_004 : Verify that the "Email (if any)" field correctly validates invalid email formats.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  await leaveMessagePage.page.fill(locs.inputEmail, 'test');
  const errorMsg = page.locator(locs.errorEmail);
  const btnSubmit = page.locator(locs.btnSubmit);
  await page.waitForTimeout(3000);
  await expect(errorMsg).toBeVisible();
  await expect(btnSubmit).toBeDisabled();
  expect(page.url()).toContain('leave-message');
});

test('TC_LMS_SVH_005 : Verify that the "Back" button correctly navigates the user back to the previous page or a specified page.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  await leaveMessagePage.page.click(locs.btnBack)
  // this case navigate to confirm or call page?
  expect(page.url()).toContain('confirm');
});

test('TC_LMS_SVH_006 : Verify that "Name" fields display correct information and are not editable.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  expect(await page.locator(locs.inputFirstName).isDisabled()).toBe(true);
  expect(page.url()).toContain('leave-message');
});

test('TC_LMS_SVH_007 : Verify that "Surname" fields display correct information and are not editable.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  expect(await page.locator(locs.inputLastName).isDisabled()).toBe(true);
  expect(page.url()).toContain('leave-message');
});

test('TC_LMS_SVH_008 : Verify that "Phone Number" fields display correct information and are not editable.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  expect(await page.locator(locs.inputTel).isDisabled()).toBe(true);
  expect(page.url()).toContain('leave-message');
});

test('TC_LMS_SVH_009 : Verify the display of placeholder text in the email input fields.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  await expect(page.locator(locs.inputEmail)).toHaveAttribute('placeholder', 'กรุณากรอกอีเมล (ถ้ามี)');
  expect(await page.locator(locs.inputEmail).isVisible()).toBe(true);
  expect(page.url()).toContain('leave-message');
});

test('TC_LMS_SVH_010 : Verify the display of the header text, labels for each field.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  await expect(page.locator(locs.titleFirstName)).toContainText(t.leaveMessage.firstNameTitle);
  await expect(page.locator(locs.titleLastName)).toContainText(t.leaveMessage.lastNameTitle);
  await expect(page.locator(locs.titleTel)).toContainText(t.leaveMessage.telTitle);
  await expect(page.locator(locs.titleEmail)).toContainText(t.leaveMessage.emailTitle);
  await expect(page.locator(locs.titleInitialSymptoms)).toContainText(t.leaveMessage.initialSymptomsTitle);
  expect(page.url()).toContain('leave-message');
});

test('TC_LMS_SVH_011 : Verify that the "Send Message" button is disabled when the "Initial Symptoms" field is empty.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  const btnSubmit = page.locator(locs.btnSubmit);
  await expect(btnSubmit).toBeDisabled();
  await expect(btnSubmit).toHaveText(t.leaveMessage.btnSubmit);
  expect(page.url()).toContain('leave-message');
});

test('TC_LMS_SVH_012 : Verify the display of the header text, labels for each field.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  const btnSubmit = page.locator(locs.btnBack);
  await expect(btnSubmit).toBeVisible();
  await expect(btnSubmit).toHaveText(t.leaveMessage.btnBack);
  expect(page.url()).toContain('leave-message');
});

test('TC_LMS_SVH_013 : Verify the display of the header text, labels for each field.',{tag: '@leaveMessage'}, async () => {
  const leaveMessagePage = new LeaveMessagePage(page);
  const cancelCallButton = page.locator(locsCall.cancelCallButton);
  const leaveMessageButton = page.locator(locsCall.leaveMessageButton);
  await cancelCallButton.click();
  await leaveMessageButton.click();
  await page.waitForTimeout(3000);
  const headerTitle = await page.locator(locs.header).textContent();
  expect(headerTitle).toContain(t.leaveMessage.header);
  expect(page.url()).toContain('leave-message');
});