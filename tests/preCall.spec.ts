import { test, expect, type Page } from '@playwright/test';
import { RegisterPage } from '../pages/RegisterPage';
import { ConsentPage } from '../pages/ConsentPage';
import { PreCallPage } from '../pages/PreCallPage';
import { ConfirmPage } from '../pages/ConfirmPage';
import { DashboardPage } from '../pages/DashboardPage';
import { LoginBackofficePage } from '../pages/LoginBackofficePage';
import data from '../data-test/register.json';
import locs from '../locs/call/index';
import i18n, { Lang } from '../i18n';

let page: Page;
let consentPage: ConsentPage;
let registerPage: RegisterPage;
let confirmPage: ConfirmPage;
let adminPage: Page;
const lang = (process.env.LANG === 'th') ? 'th' : 'en';
const t = i18n[lang];

test.beforeEach(async ({ context }) => {
  const firstName = data.userAccount[2].personalInfo.firstName;
  const lastName = data.userAccount[2].personalInfo.lastName;
  const tel = data.userAccount[2].contact.phoneNumber;

  page = await context.newPage();
  consentPage = new ConsentPage(page);
  registerPage = new RegisterPage(page);
  confirmPage = new ConfirmPage(page);
  await page.goto(process.env.URL_USER!);
  await consentPage.acceptConsent();
  await registerPage.submitRegister(firstName, lastName, tel);
  await confirmPage.isClickConfirm();
});

test.afterEach(async () => {
  await page.close();
});

test('TC_PCL_SVH_001 : Verify the "Back" button correctly navigates the user to the previous page.',{tag: '@preCall'}, async () => {
  const preCallPage = new PreCallPage(page);
  await preCallPage.isClickBack();
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('confirm');
});

test('TC_PCL_SVH_002 : Verify that the "cancel call" button ends the ongoing pre-call.',{tag: '@preCall'}, async () => {
  const preCallPage = new PreCallPage(page);
  await preCallPage.isCancelCall();
  await expect(page.locator(locs.dialogCancelCall)).toBeVisible();
});

test('TC_PCL_SVH_003 : Verify the screen transition when the doctor joins the call.',{tag: '@preCall'}, async ({ context }) => {
  const adminPage = await context.newPage();
  const loginBackofficePage = new LoginBackofficePage(adminPage);
  const dashboardPage = new DashboardPage(adminPage);
  await adminPage.goto(process.env.URL_ADMIN!);
  await loginBackofficePage.loginBackoffice('adminuat', 'temp1234*');
  await dashboardPage.isStartCall();
  await adminPage.waitForTimeout(3000);
  await page.bringToFront();
  await page.waitForTimeout(10000);
  // expect(page.url()).toContain('call');
});

test('TC_PCL_SVH_004 : Verify that the confirmation pop-up for cancellation shows up after the correct waiting time 1 minutes.',{tag: '@preCall'}, async () => {
  test.setTimeout(600000);
  await page.waitForSelector(locs.dialogCancelCall);
  await expect(page.locator(locs.dialogCancelCall)).toBeVisible();
});

test('TC_PCL_SVH_005 : Verify that after clicking the "wait for service" button in the confirmation pop-up for cancellation',{tag: '@preCall'}, async () => {
  const preCallPage = new PreCallPage(page);
  await preCallPage.isCancelCall();
  await expect(page.locator(locs.dialogCancelCall)).toBeVisible();
  await preCallPage.isWaitForService();
  await expect(page.locator(locs.dialogCancelCall)).toBeHidden();
});

test('TC_PCL_SVH_006 : Verify that after clicking the "Leave a message" buttonin the confirmation pop-up for cancellation.',{tag: '@preCall'}, async () => {
  const preCallPage = new PreCallPage(page);
  await preCallPage.isCancelCall();
  await expect(page.locator(locs.dialogCancelCall)).toBeVisible();
  await preCallPage.isLeaveMessage();
  await expect(page.locator(locs.dialogCancelCall)).toBeHidden();
  expect(page.url()).toContain('leave-message');
});

test('TC_PCL_SVH_007 : Verify the display of the waiting status message ("กรุณารอสักครู่...").',{tag: '@preCall'}, async () => {
  const waitingStatusMessage = page.locator(locs.waitingStatusMessage);
  await expect(page.locator(locs.waitingStatusMessage)).toBeVisible();
  await expect(waitingStatusMessage).toHaveText(t.preCall.waitingStatusMessage);
});

test('TC_PCL_SVH_008 : Verify the display and animation of the waveform/heartbeat icon.',{tag: '@preCall'}, async () => {
  await expect(page.locator(locs.waitingStatusMessageAnimation)).toBeVisible();
});

test('TC_PCL_SVH_009 : Verify the display of the "Back" button on the waiting screen.',{tag: '@preCall'}, async () => {
  const backButton = page.locator(locs.backButton);
  await expect(page.locator(locs.backButton)).toBeVisible();
  await expect(backButton).toHaveText(t.preCall.backButton);
});

test('TC_PCL_SVH_0010 : Verify the display of the "Cancel Call" button on the waiting screen.',{tag: '@preCall'}, async () => {
  const cancelCallButton = page.locator(locs.cancelCallButton);
  await expect(page.locator(locs.cancelCallButton)).toBeVisible();
  await expect(cancelCallButton).toHaveText(t.preCall.cancelCallButton);
});

test('TC_PCL_SVH_0011 : Verify that the main heading in the confirmation pop-up for cancellation is displayed correctly.',{tag: '@preCall'}, async () => {
  const preCallPage = new PreCallPage(page);
  const mainHeading = page.locator(locs.mainHeading);
  await preCallPage.isCancelCall();
  await expect(page.locator(locs.dialogCancelCall)).toBeVisible();
  await expect(mainHeading).toBeVisible();
  await expect(mainHeading).toHaveText(t.preCall.mainHeading);
  await page.waitForTimeout(3000);
});

test('TC_PCL_SVH_0012 : Verify that the details in the confirmation pop-up for cancellation are displayed correctly.',{tag: '@preCall'}, async () => {
  const preCallPage = new PreCallPage(page);
  const contentHeaderText = page.locator(locs.contentHeader);
  const contentText = page.locator(locs.contentText);
  await preCallPage.isCancelCall();
  await expect(page.locator(locs.dialogCancelCall)).toBeVisible();
  await expect(contentHeaderText).toBeVisible();
  await expect(contentHeaderText).toHaveText(t.preCall.contentHeader);
  await expect(contentText).toBeVisible();
  await expect(contentText).toHaveText(t.preCall.contentText);
});

test('TC_PCL_SVH_0013 : Verify the display of the "leave a message" button on the confirmation pop-up for cancellation.',{tag: '@preCall'}, async () => {
  const preCallPage = new PreCallPage(page);
  const leaveMessageButton = page.locator(locs.leaveMessageButton);
  await preCallPage.isCancelCall();
  await expect(page.locator(locs.dialogCancelCall)).toBeVisible();
  await expect(leaveMessageButton).toBeVisible();
  await expect(leaveMessageButton).toHaveText(t.preCall.leaveMessageButton);
});

test('TC_PCL_SVH_0014 : Verify the display of the "waiting for service" button on the confirmation pop-up for cancellation.',{tag: '@preCall'}, async () => {
  const preCallPage = new PreCallPage(page);
  const waitForServiceButton = page.locator(locs.waitForServiceButton);
  await preCallPage.isCancelCall();
  await expect(page.locator(locs.dialogCancelCall)).toBeVisible();
  await expect(waitForServiceButton).toBeVisible();
  await expect(waitForServiceButton).toHaveText(t.preCall.waitForServiceButton);
});

test.afterAll(async ({ browser }) => {
  adminPage = await browser.newPage();
  const loginBackofficePage = new LoginBackofficePage(adminPage);
  const dashboardPage = new DashboardPage(adminPage);
  await adminPage.goto(process.env.URL_ADMIN!);
  await loginBackofficePage.loginBackoffice('adminuat', 'temp1234*');
  await dashboardPage.isClickDashboard();
  await dashboardPage.isClickNurseRoom();
  // await dashboardPage.isClickWaitingNurse();
  await dashboardPage.isClickInfoButton(data.userAccount[2].personalInfo.fullName);
  await dashboardPage.isEndCallQueue();
  await adminPage.waitForTimeout(1000);
  await dashboardPage.isConfirmEndCallQueue();
  await adminPage.waitForTimeout(1000);
  await dashboardPage.isClickOkButton();
  await page.close();
});