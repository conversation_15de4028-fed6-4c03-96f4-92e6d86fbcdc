import { test, expect, type Page, } from '@playwright/test';
import { RegisterPage } from '../pages/RegisterPage';
import { ConsentPage } from '../pages/ConsentPage';
import data from '../data-test/register.json';
import locs from '../locs/register/index';
import i18n, { Lang } from '../i18n';

let page: Page;
let consentPage: ConsentPage;
let registerPage: RegisterPage;
const lang = (process.env.LANG === 'th') ? 'th' : 'en';
const t = i18n[lang];

test.beforeEach(async ({ context }) => {
  page = await context.newPage();
  consentPage = new ConsentPage(page);
  registerPage = new RegisterPage(page);
  await page.goto(process.env.URL_USER!);
  await consentPage.acceptConsent();

});

test.afterEach(async () => {
  await page.close();
});

test('TC_REG_SVH_001 : Verify successful registration with valid input.',{tag: '@register'}, async () => {
  const firstName = data.userAccount[3].personalInfo.firstName;
  const lastName = data.userAccount[3].personalInfo.lastName;
  const tel = data.userAccount[3].contact.phoneNumber;
  await registerPage.submitRegister(firstName, lastName, tel);
  expect(page.url()).toContain('register');
});

test('TC_REG_SVH_002 : Verify that an error is displayed when the first name is empty.',{tag: '@register'}, async () => {
  const dataUser = structuredClone(data);
  dataUser.userAccount[3].personalInfo.firstName = '';
  const btnSubmit = page.locator(locs.submitButton);
  const firstName = dataUser.userAccount[3].personalInfo.firstName;
  const lastName = data.userAccount[3].personalInfo.lastName;
  const tel = data.userAccount[3].contact.phoneNumber;
  await registerPage.submitRegister(firstName, lastName, tel);

  const errorFirstName = page.locator(locs.errorFirstName);
  await expect(errorFirstName).toBeVisible();
  await expect(errorFirstName).toHaveText(t.register.errorFirstName);
  await expect(btnSubmit).toBeDisabled();
});

test('TC_REG_SVH_003 : Verify that an error is displayed when the last name is empty.',{tag: '@register'}, async () => {
  const dataUser = structuredClone(data);
  dataUser.userAccount[3].personalInfo.lastName = '';
  const btnSubmit = page.locator(locs.submitButton);
  const firstName = data.userAccount[3].personalInfo.firstName;
  const lastName = dataUser.userAccount[3].personalInfo.lastName;
  const tel = data.userAccount[3].contact.phoneNumber;
  await registerPage.submitRegister(firstName, lastName, tel);

  const errorLastName = page.locator(locs.errorLastName);
  await expect(errorLastName).toBeVisible();
  await expect(errorLastName).toHaveText(t.register.errorLastName);
  await expect(btnSubmit).toBeDisabled();
});

test('TC_REG_SVH_004 : Verify that an error is displayed when the phone number is empty.',{tag: '@register'}, async () => {
  const dataUser = structuredClone(data);
  dataUser.userAccount[3].contact.phoneNumber = '';
  const btnSubmit = page.locator(locs.submitButton);
  const firstName = data.userAccount[3].personalInfo.firstName;
  const lastName = data.userAccount[3].personalInfo.lastName;
  const tel = dataUser.userAccount[3].contact.phoneNumber;
  await registerPage.submitRegister(firstName, lastName, tel);

  const errorTel = page.locator(locs.errorTel);
  await expect(errorTel).toBeVisible();
  await expect(errorTel).toHaveText(t.register.errorTel);
  await expect(btnSubmit).toBeDisabled();
});

test('TC_REG_SVH_005 : Verify that the form detects invalid phone number formats.',{tag: '@register'}, async () => {
  const dataUser = structuredClone(data);
  dataUser.userAccount[3].contact.phoneNumber = '0666333';
  const btnSubmit = page.locator(locs.submitButton);
  const firstName = data.userAccount[3].personalInfo.firstName;
  const lastName = data.userAccount[3].personalInfo.lastName;
  const tel = dataUser.userAccount[3].contact.phoneNumber;
  await registerPage.submitRegister(firstName, lastName, tel);

  const errorFormatTel = page.locator(locs.errorFormatTel);
  await expect(errorFormatTel).toBeVisible();
  await expect(errorFormatTel).toHaveText(t.register.errorFormatTel);
  await expect(btnSubmit).toBeDisabled();
});

test('TC_REG_SVH_006 : Verify that the instruction message and contact number are displayed.',{tag: '@register'}, async () => {
  const title = page.locator(locs.title);
  const subTitle = page.locator(locs.subTitle);
  await expect(title).toBeVisible();
  await expect(subTitle).toBeVisible();
  await expect(title).toHaveText(t.register.title);
  await expect(subTitle).toHaveText(t.register.subTitle);
});


test('TC_REG_SVH_007 : Verify that the registration form and all input elements are displayed.',{tag: '@register'}, async () => {
  const firstNameTitle = page.locator(locs.firstNameTitle);
  const lastNameTitle = page.locator(locs.lastNameTitle);
  const telTitle = page.locator(locs.telTitle);
  await expect(firstNameTitle).toBeVisible();
  await expect(lastNameTitle).toBeVisible();
  await expect(telTitle).toBeVisible();
  await expect(firstNameTitle).toHaveText(t.register.firstNameTitle);
  await expect(lastNameTitle).toHaveText(t.register.lastNameTitle);
  await expect(telTitle).toHaveText(t.register.telTitle);
});

test('TC_REG_SVH_008 : Verify that the first name field displays a placeholder.',{tag: '@register'}, async () => {
  const firstNamePlaceholder = page.locator(locs.firstNamePlaceholder);
  await expect(firstNamePlaceholder).toBeVisible();
  await expect(firstNamePlaceholder).toHaveAttribute('placeholder', t.register.firstNamePlaceholder);
});

test('TC_REG_SVH_009 : Verify that the last name field displays a placeholder.',{tag: '@register'}, async () => {
  const lastNamePlaceholder = page.locator(locs.lastNamePlaceholder);
  await expect(lastNamePlaceholder).toBeVisible();
  await expect(lastNamePlaceholder).toHaveAttribute('placeholder', t.register.lastNamePlaceholder);
});

test('TC_REG_SVH_010 : Verify that the phone number field displays a placeholder.',{tag: '@register'}, async () => {
  const telPlaceholder = page.locator(locs.telPlaceholder);
  await expect(telPlaceholder).toBeVisible();
  await expect(telPlaceholder).toHaveAttribute('placeholder', t.register.telPlaceholder);
});

test('TC_REG_SVH_011 : Verify that the “Registetion” button is visible and styled correctly.',{tag: '@register'}, async () => {
  const btnSubmit = page.locator(locs.submitButton);
  await expect(btnSubmit).toBeVisible();
  await expect(btnSubmit).toHaveText(t.register.btnSubmit);
});
