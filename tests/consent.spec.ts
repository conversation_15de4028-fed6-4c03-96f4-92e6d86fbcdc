import { test, expect, type Page } from '@playwright/test';
import { ConsentPage } from '../pages/ConsentPage';
import locs from '../locs/consent/index';
import i18n, { Lang } from '../i18n';

let page: Page;
let consentPage: ConsentPage;
const lang = (process.env.LANG === 'th') ? 'th' : 'en';
const t = i18n[lang];

test.beforeEach(async ({ context }) => {
  page = await context.newPage();
  consentPage = new ConsentPage(page);
  await page.goto(process.env.URL_USER!);
});

test.afterEach(async () => {
  await page.close();
});

test('TC_CNS_SVH_001 : Verify that the user can proceed after checking the consent checkbox and clicking the "next" button.',{tag: '@consent'}, async () => {
  const element = page.locator(locs.consentModal);
  await consentPage.acceptConsent();
  await expect(element).toBeHidden();
});

test('TC_CNS_SVH_002 : Verify that the "next" button is enabled only after the user has clicked the terms and conditions',{tag: '@consent'}, async () => {
  const btnConfirm = page.locator(locs.confirmButton);
  await expect(btnConfirm).toBeDisabled();
  await consentPage.isClickCheckbox();
  await expect(btnConfirm).toBeEnabled();
});

test('TC_CNS_SVH_003 : Verify that clicking "next" button without checking the consent checkbox does not allow proceeding',{tag: '@consent'}, async () => {
  const btnConfirm = page.locator(locs.confirmButton);
  await expect(btnConfirm).toBeDisabled();
});

test('TC_CNS_SVH_004 : Verify that the terms header displays the correct title and subtitle.',{tag: '@consent'}, async () => {
  const titleHeader = page.locator(locs.consentHeader);
  const title = await titleHeader.innerText();
  expect(title).toContain(t.consent.headers);
  expect(title).toContain(t.consent.subHeader);
  await expect(titleHeader).toBeVisible();
});

test('TC_CNS_SVH_005 : Verify that the terms and conditions content is fully visible and correctly displayed',{tag: '@consent'}, async () => {
  const content = page.locator(locs.consentContent);
  await expect(content).toBeVisible();
});

test('TC_CNS_SVH_006 : Verify that the checkbox and its label are present below the content.',{tag: '@consent'}, async () => {
  const titleCheckbox = page.locator(locs.consentLabel);
  await expect(titleCheckbox).toBeVisible();
  await expect(titleCheckbox).toHaveText(t.consent.lblCheckbox);
});

test('TC_CNS_SVH_007 : Verify that the "next" button is disabled before the checkbox is selected.',{tag: '@consent'}, async () => {
  const btnConfirm = page.locator(locs.confirmButton);
  const btnText = await btnConfirm.innerText();
  await expect(btnConfirm).toBeDisabled();
  expect(btnText).toContain(t.consent.btnNext);
});

test('TC_CNS_SVH_008 : Verify that the "next" button becomes enabled after checking the checkbox.',{tag: '@consent'}, async () => {
  const btnConfirm = page.locator(locs.confirmButton);
  const btnText = await btnConfirm.innerText();
  await consentPage.isClickCheckbox();
  await expect(btnConfirm).toBeEnabled();
  expect(btnText).toContain(t.consent.btnNext);
});