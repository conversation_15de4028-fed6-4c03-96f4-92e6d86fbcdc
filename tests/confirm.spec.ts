import { test, expect, type Page, } from '@playwright/test';
import { RegisterPage } from '../pages/RegisterPage';
import { ConsentPage } from '../pages/ConsentPage';
import { ConfirmPage } from '../pages/ConfirmPage';
import { DashboardPage } from '../pages/DashboardPage';
import { LoginBackofficePage } from '../pages/LoginBackofficePage';
import data from '../data-test/register.json';
import locs from '../locs/confirm/index';
import i18n, { Lang } from '../i18n';

let page: Page;
let consentPage: ConsentPage;
let registerPage: RegisterPage;
let adminPage: Page;
let statusCall: boolean = false;
const lang = (process.env.LANG === 'th') ? 'th' : 'en';
const t = i18n[lang];

test.beforeEach(async ({ context }) => {
  page = await context.newPage();
  consentPage = new ConsentPage(page);
  registerPage = new RegisterPage(page);
  await page.goto(process.env.URL_USER!);
  await consentPage.acceptConsent();

  const firstName = data.userAccount[0].personalInfo.firstName;
  const lastName = data.userAccount[0].personalInfo.lastName;
  const tel = data.userAccount[0].contact.phoneNumber;
  await registerPage.submitRegister(firstName, lastName, tel);
});

test.afterEach(async ({context}) => {
  if(statusCall === true){
    adminPage = await context.newPage();
    const loginBackofficePage = new LoginBackofficePage(adminPage);
    const dashboardPage = new DashboardPage(adminPage);
    await adminPage.goto(process.env.URL_ADMIN!);
    await loginBackofficePage.loginBackoffice('adminuat', 'temp1234*');
    await dashboardPage.isClickDashboard();
    await dashboardPage.isClickWaitingNurse();
    await dashboardPage.isClickInfoButton(data.userAccount[0].personalInfo.fullName);
    await dashboardPage.isEndCallQueue();
    await adminPage.waitForTimeout(1000);
    await dashboardPage.isConfirmEndCallQueue();
    await adminPage.waitForTimeout(1000);
    await dashboardPage.isClickOkButton();
    await page.close();
    statusCall = false;
  } else {
    await page.close();
  }
});

test('TC_CFM_SVH_001 : Verify that clicking the “Start Consultation” button initiates the consultation process.',{tag: '@confirm'}, async () => {
  const confirmPage = new ConfirmPage(page);
  await confirmPage.isClickConfirm();
  await page.waitForTimeout(3000);
  statusCall = true;
  expect(page.url()).toContain('call');
});

test.skip('TC_CFM_SVH_002 : Verify that clicking the “Back” button navigates the user to the previous registration page.',{tag: '@confirm'}, async () => {
  console.log('This test case has been cancelled because the related UI element (button) no longer exists in the current version of the system.');
});

test('TC_CFM_SVH_003 : Verify that the main heading “ขั้นตอนการรับบริการ” is displayed correctly.',{tag: '@confirm'}, async () => {
  const confirmPage = new ConfirmPage(page);
  const heading = confirmPage.page.locator(locs.heading);
  await expect(heading).toBeVisible();
  await expect(heading).toHaveText(t.confirm.header);
  expect(page.url()).toContain('confirm');
});

test('TC_CFM_SVH_004 : Verify that the subheading “Samitivej Virtual Hospital” is shown below the main heading.',{tag: '@confirm'}, async () => {
  const confirmPage = new ConfirmPage(page);
  const subheading = confirmPage.page.locator(locs.subheading);
  await expect(subheading).toBeVisible();
  await expect(subheading).toHaveText(t.confirm.subHeader);
  expect(page.url()).toContain('confirm');
});

test('TC_CFM_SVH_005 : Verify that all 5 service step descriptions are accurate and fully displayed.',{tag: '@confirm'}, async () => {
  const confirmPage = new ConfirmPage(page);
  const img = confirmPage.page.locator('img[alt="svvh-steps"]');
  await expect(img).toBeVisible();
  const src = await img.getAttribute('src');
  expect(src).toContain('steps_th');
  expect(page.url()).toContain('confirm');
});

test('TC_CFM_SVH_006 : Verify that the “Start Consultation” button is visible and styled correctly.',{tag: '@confirm'}, async () => {
  const confirmPage = new ConfirmPage(page);
  const btn = confirmPage.page.locator(locs.btnConfirm);
  await expect(btn).toBeVisible();
  await expect(btn).toHaveText(t.confirm.btnConfirm);
  expect(page.url()).toContain('confirm');
});

test.skip('TC_CFM_SVH_007 : Verify that the “Back” button is visible and labeled correctly.',{tag: '@confirm'}, async () => {
  console.log('This test case has been cancelled because the related UI element (button) no longer exists in the current version of the system.');
});
