import { test, expect, type Page } from '@playwright/test';
import { RegisterPage } from '../pages/RegisterPage';
import { ConsentPage } from '../pages/ConsentPage';
import { PreCallPage } from '../pages/PreCallPage';
import { ConfirmPage } from '../pages/ConfirmPage';
import { StartCallPage } from '../pages/StartCallPage';
import { InCallPage } from '../pages/IncallPage';
import { DashboardPage } from '../pages/DashboardPage';
import { LoginBackofficePage } from '../pages/LoginBackofficePage';
import { FileUploadHelper } from '../utils/fileUploadHelper';
import data from '../data-test/register.json';
import locs from '../locs/call/index';
import i18n from '../i18n';

let page: Page;
let consentPage: ConsentPage;
let registerPage: RegisterPage;
let confirmPage: ConfirmPage;
let preCallPage: PreCallPage;
let startCallPage: StartCallPage;
let adminPage: Page;
const lang = (process.env.LANG === 'th') ? 'th' : 'en';
const t = i18n[lang];

test.beforeEach(async ({ context }) => {
  test.setTimeout(600000);
  const firstName = data.userAccount[0].personalInfo.firstName;
  const lastName = data.userAccount[0].personalInfo.lastName;
  const tel = data.userAccount[0].contact.phoneNumber;

  page = await context.newPage();
  consentPage = new ConsentPage(page);
  registerPage = new RegisterPage(page);
  confirmPage = new ConfirmPage(page);
  await page.goto(process.env.URL_USER!);
  await consentPage.acceptConsent();
  await registerPage.submitRegister(firstName, lastName, tel);
  await confirmPage.isClickConfirm();

  adminPage = await context.newPage();
  const loginBackofficePage = new LoginBackofficePage(adminPage);
  const dashboardPage = new DashboardPage(adminPage);
  await adminPage.goto(process.env.URL_ADMIN!);
  await loginBackofficePage.loginBackoffice('adminuat', 'temp1234*');
  await dashboardPage.isStartCall();
  await adminPage.waitForTimeout(3000);
  await page.bringToFront();
  await page.waitForTimeout(10000);

  startCallPage = new StartCallPage(page);
  await startCallPage.isClickCall();
});

test.afterEach(async () => {
  const dashboardPage = new DashboardPage(adminPage);
  await adminPage.bringToFront();
  await dashboardPage.isClickDashboard();
  await dashboardPage.isClickNurseRoom();
  await dashboardPage.isClickInfoButton(data.userAccount[0].personalInfo.fullName);
  await dashboardPage.isEndCallQueue();
  await adminPage.waitForTimeout(1000);
  await dashboardPage.isConfirmEndCallQueue();
  await adminPage.waitForTimeout(1000);
  await dashboardPage.isClickOkButton();
  await page.close();
});

test('TC_IN_CALL_SVH_001 : Verify that the user can turn on the microphone.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickMicrophoneOnButton();
  await expect(page.locator(locs.microphoneOnButton)).toBeVisible();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_002 : Verify that the user can turn off the microphone.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickMicrophoneOnButton();
  await expect(page.locator(locs.microphoneOnButton)).toBeVisible();
  await inCallPage.page.waitForTimeout(3000);
  await inCallPage.isClickMicrophoneOffButton();
  await expect(page.locator(locs.microphoneOffButton)).toBeVisible();
  await inCallPage.page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_003 : Verify that the user can turn on the camera and show video.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickCameraOnButton();
  await expect(page.locator(locs.cameraOnButton)).toBeVisible();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_004 : Verify that the user can turn off the camera and show the avatar instead.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickCameraOnButton();
  await expect(page.locator(locs.cameraOnButton)).toBeVisible();
  await inCallPage.page.waitForTimeout(3000);
  await inCallPage.isClickCameraOffButton();
  await expect(page.locator(locs.microphoneOffButton)).toBeVisible();
  await inCallPage.page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_005 : Verify that the user can raise their hand during a call.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickRaiseHandButton();
  await expect(page.locator(locs.lowerHandButton)).toBeVisible();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_006 : Verify that the user can lower their hand after raising It.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickRaiseHandButton();
  await expect(page.locator(locs.lowerHandButton)).toBeVisible();
  await page.waitForTimeout(3000);
  await inCallPage.isClickLowerHandButton();
  await expect(page.locator(locs.raiseHandButton)).toBeVisible();
  await inCallPage.page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_007 : Verify that the user can start presenting their screen.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  // Skip this test case because the element is a separate popup that needs additional conditions to be checked.
});

test('TC_IN_CALL_SVH_008 : Verify that the user can leave the call and exit to the home screen.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isEndCall();
  await page.waitForTimeout(3000);
  await inCallPage.isConfirmLeaveCall();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_009 : Verify that the people panel opens when the people button is clicked.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickPeoplePanel();
  await expect(page.locator(locs.peopleHeading)).toBeVisible();
  await expect(page.locator(locs.peopleHeading)).toHaveText(t.inCall.peopleHeading);
  await page.waitForTimeout(3000);
});

test.skip('TC_IN_CALL_SVH_010 : Verify that removing a person removes it from the peoples view.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  // ตอนนี้กล้องฝั่งพยาบาลยังไม่แสดง ข้ามเคสนี้ไปทำเคสอื่นก่อน
});

test('TC_IN_CALL_SVH_011 : Verify that clicking the people button again toggles (closes) the people panel.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickPeoplePanel();
  await expect(page.locator(locs.peopleHeading)).toBeVisible();
  await inCallPage.isClickPeoplePanel();
  await expect(page.locator(locs.peopleHeading)).toBeHidden();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_012 : Verify that clicking the close (X) button on the people panel closes the panel.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickPeoplePanel();
  await expect(page.locator(locs.peopleHeading)).toBeVisible();
  await inCallPage.isClickClosePanel();
  await expect(page.locator(locs.peopleHeading)).toBeHidden();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_0013 : Verify that the chat panel opens when the chat button is clicked.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await expect(page.locator(locs.chatHeading)).toHaveText(t.inCall.chatHeading);
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_014 : Verify that clicking the chat button again toggles (closes) the chat panel.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeHidden();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_015 : Verify that clicking the close (X) button on the chat panel closes the panel.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await inCallPage.isClickClosePanel();
  await expect(page.locator(locs.chatHeading)).toBeHidden();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_016 : Verify that a user can send a text message in the chat panel.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  const message = 'Hello'
  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await inCallPage.isSendMessage(message);
  await inCallPage.isVerifyMessage(message)
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_017 : Verify that the chat panel supports uploading and sending PNG image files.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  const filePath = './data-test/img/test-image.jpg';

  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await inCallPage.isSendBoxAttachFile(filePath);
  await page.waitForTimeout(5000);
});

test('TC_IN_CALL_SVH_018 : Verify that the chat panel supports uploading and sending image files with supported formats: .JPEG.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  const filePath = './data-test/img/upload-test.jpeg';

  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await inCallPage.isSendBoxAttachFile(filePath);
  await page.waitForTimeout(5000);
});

test('TC_IN_CALL_SVH_019 : Verify that the chat panel supports uploading and sending image files with supported formats: .PNG.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  const filePath = './data-test/img/image-png.png';

  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await inCallPage.isSendBoxAttachFile(filePath);
  await page.waitForTimeout(5000);
});

test('TC_IN_CALL_SVH_020 : Verify that deleting a sent message removes it from the chat view.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  const message = 'Hello';
  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await inCallPage.isSendMessage(message);
  await inCallPage.isVerifyMessage(message);
  await page.waitForTimeout(3000);
  await inCallPage.isDeleteMessage(message);
  await expect(page.locator(`//div[@aria-label='You said ${message}']`)).toBeHidden();
});

test('TC_IN_CALL_SVH_021 : Verify that the chat panel supports uploading and sending image files with supported formats: .PNG.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  const message = 'Hello';
  const newMessage = 'Hello World';
  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await inCallPage.isSendMessage(message);
  await inCallPage.isVerifyMessage(message);
  await page.waitForTimeout(3000);
  await inCallPage.isEditMessage(message, newMessage);
  await inCallPage.isVerifyMessage(newMessage);
});

test('TC_IN_CALL_SVH_022 : Verify that user avatars are displayed correctly.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.userAvatar)).toBeVisible();
});

test('TC_IN_CALL_SVH_023 : Verify that the microphone (Mic) button is displayed correctly.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.microphoneOffButton)).toBeVisible();
  await expect(page.locator(locs.micLabel)).toBeVisible();
  await expect(page.locator(locs.micLabel)).toHaveText(t.inCall.micLabel);
});

test('TC_IN_CALL_SVH_024 : Verify that the camera button is displayed correctly.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.cameraOffButton)).toBeVisible();
  await expect(page.locator(locs.cameraLabel)).toBeVisible();
  await expect(page.locator(locs.cameraLabel)).toHaveText(t.inCall.cameraLabel);
});

test('TC_IN_CALL_SVH_025 : Verify that the raise button is displayed correctly.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.raiseHandButton)).toBeVisible();
  await expect(page.locator(locs.raiseHandLabel)).toBeVisible();
  await expect(page.locator(locs.raiseHandLabel)).toHaveText(t.inCall.raiseHandLabel);
});

test('TC_IN_CALL_SVH_026 : Verify that the present (Screen Share) button is displayed correctly.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.presentScreenOnButton)).toBeVisible();
  await expect(page.locator(locs.presentScreenLabel)).toBeVisible();
  await expect(page.locator(locs.presentScreenLabel)).toHaveText(t.inCall.presentScreenLabel);
});

test('TC_IN_CALL_SVH_027 : Verify that the end call button is displayed correctly.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.endCallButton)).toBeVisible();
});

test('TC_IN_CALL_SVH_028 : Verify that the people button is displayed correctly.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.peopleButton)).toBeVisible();
  await expect(page.locator(locs.peopleLabel)).toBeVisible();
  await expect(page.locator(locs.peopleLabel)).toHaveText(t.inCall.peopleLabel);
});

test('TC_IN_CALL_SVH_029 : Verify that the chat button is displayed correctly.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.chatButton)).toBeVisible();
  await expect(page.locator(locs.chatLabel)).toBeVisible();
  await expect(page.locator(locs.chatLabel)).toHaveText(t.inCall.chatLabel);
});

test('TC_IN_CALL_SVH_030 : Verify that the chat panel is visible on the right side.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.chatButton)).toBeVisible();
  await expect(page.locator(locs.chatLabel)).toBeVisible();
  await expect(page.locator(locs.chatLabel)).toHaveText(t.inCall.chatLabel);

  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.inputChat)).toBeVisible();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_031 : Verify the appearance of the message input field.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.chatButton)).toBeVisible();
  await expect(page.locator(locs.chatLabel)).toBeVisible();
  await expect(page.locator(locs.chatLabel)).toHaveText(t.inCall.chatLabel);

  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.inputChat)).toBeVisible();
  await expect(page.locator(locs.inputChat)).toHaveAttribute('placeholder', 'Enter a message');
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_032 : Verify the display of the attached image or file message.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await expect(page.locator(locs.chatButton)).toBeVisible();
  await expect(page.locator(locs.chatLabel)).toBeVisible();
  await expect(page.locator(locs.chatLabel)).toHaveText(t.inCall.chatLabel);

  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.inputChat)).toBeVisible();
  await expect(page.locator(locs.iconSendBoxAttachFile)).toBeVisible();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_033 : Verify that sent text messages are displayed correctly.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  const message = 'Hello';
  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await inCallPage.isSendMessage(message);
  await inCallPage.isVerifyMessage(message);

  await expect(page.locator(`//div[@aria-label='You said ${message}']`)).toBeVisible();
  await expect(page.locator(locs.dateTimeLabel)).toBeVisible();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_034 : Verify that the dropdown menu appears when the button is clicked on the message.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  const message = 'Hello';
  await inCallPage.isClickChatPanel();
  await expect(page.locator(locs.chatHeading)).toBeVisible();
  await inCallPage.isSendMessage(message);
  await inCallPage.isVerifyMessage(message);

  await expect(page.locator(`//div[@aria-label='You said ${message}']`)).toBeVisible();
  await expect(page.locator(locs.dateTimeLabel)).toBeVisible();
  await inCallPage.page.click(`//div[@aria-label='You said ${message}']`);
  await inCallPage.page.waitForTimeout(1000);
  await inCallPage.page.click(locs.iconAcionMsg);
  await expect(page.locator(locs.editMessageButton)).toBeVisible();
  await expect(page.locator(locs.deleteMessageButton)).toBeVisible();
  await page.waitForTimeout(3000);
});

test('TC_IN_CALL_SVH_035 : Verify that the people panel is visible on the right side.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  await inCallPage.isClickPeoplePanel();
  await expect(page.locator(locs.peopleHeading)).toBeVisible();
  await page.waitForTimeout(3000);
});

test.skip('TC_IN_CALL_SVH_036 : Verify that the dropdown menu appears when the button is clicked on the people.', {tag: '@inCall'}, async () => {
  const inCallPage = new InCallPage(page);
  console.log('Delete to menu fuction in dropdown people');
});



