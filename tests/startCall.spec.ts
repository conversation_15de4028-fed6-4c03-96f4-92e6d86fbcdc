import { test, expect, type Page } from '@playwright/test';
import { RegisterPage } from '../pages/RegisterPage';
import { ConsentPage } from '../pages/ConsentPage';
import { PreCallPage } from '../pages/PreCallPage';
import { StartCallPage } from '../pages/StartCallPage';
import { ConfirmPage } from '../pages/ConfirmPage';
import { DashboardPage } from '../pages/DashboardPage';
import { LoginBackofficePage } from '../pages/LoginBackofficePage';
import data from '../data-test/register.json';
import locs from '../locs/call/index';
import i18n from '../i18n';

let page: Page;
let consentPage: ConsentPage;
let registerPage: RegisterPage;
let confirmPage: ConfirmPage;
let preCallPage: PreCallPage;
let startCallPage: StartCallPage;
let adminPage: Page;
const lang = (process.env.LANG === 'th') ? 'th' : 'en';
const t = i18n[lang];

test.beforeEach(async ({ context }) => {
  test.setTimeout(600000);
  const firstName = data.userAccount[0].personalInfo.firstName;
  const lastName = data.userAccount[0].personalInfo.lastName;
  const tel = data.userAccount[0].contact.phoneNumber;

  page = await context.newPage();
  consentPage = new ConsentPage(page);
  registerPage = new RegisterPage(page);
  confirmPage = new ConfirmPage(page);
  await page.goto(process.env.URL_USER!);
  await consentPage.acceptConsent();
  await registerPage.submitRegister(firstName, lastName, tel);
  await confirmPage.isClickConfirm();

  adminPage = await context.newPage();
  const loginBackofficePage = new LoginBackofficePage(adminPage);
  const dashboardPage = new DashboardPage(adminPage);
  await adminPage.goto(process.env.URL_ADMIN!);
  await loginBackofficePage.loginBackoffice('adminuat', 'temp1234*');
  await dashboardPage.isStartCall();
  await adminPage.waitForTimeout(3000);
  await page.bringToFront();
  await page.waitForTimeout(10000);

  // preCallPage = new PreCallPage(page);
  // await preCallPage.isClickStartCall();
});

test.afterEach(async () => {
  const dashboardPage = new DashboardPage(adminPage);
  await adminPage.bringToFront();
  await dashboardPage.isClickDashboard();
  await dashboardPage.isClickNurseRoom();
  await dashboardPage.isClickInfoButton(data.userAccount[0].personalInfo.fullName);
  await dashboardPage.isEndCallQueue();
  await adminPage.waitForTimeout(1000);
  await dashboardPage.isConfirmEndCallQueue();
  await adminPage.waitForTimeout(1000);
  await dashboardPage.isClickOkButton();
  await page.close();
});

test('TC_SCL_SVH_001 : Verify the list of available cameras when clicking on the "Camera" dropdown.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickCameraDropdown();
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_002 : Verify that the camera turns on successfully and the video feed is displayed.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickCameraOffButton();
  await expect(startCallPage.page.locator(locs.cameraOnButton)).toBeVisible();
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_003 : Verify that the camera turns off successfully and the video feed is undisplayed.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickCameraOffButton();
  await expect(startCallPage.page.locator(locs.cameraOnButton)).toBeVisible();
  await page.waitForTimeout(3000);
  await startCallPage.isClickCameraOnButton();
  await expect(startCallPage.page.locator(locs.cameraOffButton)).toBeVisible();
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_004 : Verify the list of available microphones when clicking on the microphone dropdown.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickMicrophoneDropdown();
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_005 : Verify the list of available speakers when clicking on the speaker dropdown.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickSpeakerDropdown();
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_006 : Verify that the microphone turns on and is ready for use.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickMicrophoneOffButton();
  await expect(startCallPage.page.locator(locs.microphoneOnButton)).toBeVisible();
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_007 : Verify that the microphone turns off and is not ready for use.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickMicrophoneOffButton();
  await expect(startCallPage.page.locator(locs.microphoneOnButton)).toBeVisible();
  await page.waitForTimeout(3000);
  await startCallPage.isClickMicrophoneOnButton();
  await expect(startCallPage.page.locator(locs.microphoneOffButton)).toBeVisible();
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_008 : Verify by clicking the "Start call" button with default device selections.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickMicrophoneOffButton();
  await startCallPage.isClickCall();
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_009 : Verify by clicking the "Start call" button after selecting different camera and microphone/speaker devices.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickMicrophoneOffButton();
  await startCallPage.isClickCameraOffButton();
  await startCallPage.isClickCall();
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_010 : Verify the display of the "Virtual Hospital" logo.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await expect(page.locator(locs.logo)).toBeVisible();
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_011 : Verify the display of the "Start a call" text.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  const btn = startCallPage.page.locator(locs.startCallText);
  await expect(btn).toBeVisible();
  await expect(btn).toHaveText(t.startCall.startCallText);
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_012 : Verify the display of the camera preview area.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  // await expect(page.locator(locs.cameraPreview)).toBeVisible();
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_013 : Verify the display of the "Camera" device selection section.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickMicrophoneDropdown();
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_014 : Verify the display of the "Sound" device selection section.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickSpeakerDropdown();
  await page.waitForTimeout(3000);
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_015 : Verify the display of the "Microphone" device selection section.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  await startCallPage.isClickMicrophoneOffButton();
  await expect(startCallPage.page.locator(locs.microphoneOnButton)).toBeVisible();
  expect(page.url()).toContain('call');
});

test('TC_SCL_SVH_016 : Verify the display of the "Start call" button.', {tag: '@startCall'}, async () => {
  startCallPage = new StartCallPage(page);
  const btn = startCallPage.page.locator(locs.startCallButton);
  await expect(btn).toBeVisible();
  await expect(btn).toHaveText(t.startCall.startCallButton);
  expect(page.url()).toContain('call');
});
