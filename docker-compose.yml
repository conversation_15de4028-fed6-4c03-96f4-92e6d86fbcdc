version: '3.8'

services:
  playwright:
    build: .
    environment:
      - ENV=dev
    volumes:
      - ./:/app
      - ./playwright-report:/app/playwright-report
    working_dir: /app
    command: sh -c "npx playwright test --reporter=html --output=playwright-report && ls -la playwright-report"

  mysql:
    image: mysql:8.0
    container_name: mysql_container
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: tutorial
      # MYSQL_USER: root
      # MYSQL_PASSWORD: user
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    platform: linux/amd64
    container_name: phpmyadmin_container
    restart: always
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root
    ports:
      - "8080:80"

volumes:
  mysql-data:
    driver: local