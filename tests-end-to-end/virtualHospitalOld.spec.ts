import { test, expect, type Page } from '@playwright/test';
import { ConsentPage } from '../pages/VirtualHospitalOld';
import locs from '../locs/consent/index';
import i18n, { Lang } from '../i18n';

let page: Page;
let patientPage: ConsentPage;
let adminPage: ConsentPage;
const lang = (process.env.LANG === 'th') ? 'th' : 'en';

test.beforeEach(async ({ context }) => {
  page = await context.newPage();
  patientPage = new ConsentPage(page);
  await page.goto(process.env.URL_USER!);
});

test.afterEach(async () => {
  // await page.close();
});

test('TC_CNS_SVH_001 : Verify patient waiting to join the call.',{tag: '@oldVersion'}, async ({ context }) => {
  await patientPage.isClickAccept();
  await patientPage.page.waitForTimeout(5000);
  await patientPage.isClickAccept();
  await patientPage.isInputForm();
  await patientPage.isClickSubmit();
  await patientPage.isClickConfirm();

  const adminPageBrowser = await context.newPage();
  adminPage = new ConsentPage(adminPageBrowser);
  await adminPage.page.goto(process.env.URL_ADMIN!);
  await adminPage.isLoginBackOffice();
  await adminPage.isStartCall();
  await patientPage.page.waitForTimeout(3000);

  // ตรวจสอบและรอให้ call stable
  try {
    console.log('🔄 Bringing patient page to front...');
    await patientPage.page.bringToFront();

    // ใช้ method ใหม่สำหรับรอให้ call stable
    console.log('⏳ Waiting for call to be stable...');
    const isStable = await patientPage.waitForCallStable(30000);

    if (!isStable) {
      console.log('⚠️ Call did not become stable within timeout');

      // ตรวจสอบสถานะปัจจุบันของ page
      const currentUrl = patientPage.page.url();
      console.log('📍 Final patient page URL:', currentUrl);

      // ตรวจสอบว่า page ยัง active อยู่หรือไม่
      const isCallActive = await patientPage.isCallPageActive();
      console.log('📊 Is call page still active:', isCallActive);

      if (!isCallActive) {
        console.log('❌ Call page is no longer active - possible auto-disconnect occurred');
      }
    } else {
      console.log('✅ Call is stable and ready');

      // ตรวจสอบสถานะสุดท้าย
      const finalUrl = patientPage.page.url();
      console.log('📍 Final stable URL:', finalUrl);
    }

  } catch (error) {
    console.log('⚠️ Error during call establishment:', error);
    console.log('📍 Error URL:', patientPage.page.url());
    // ไม่ throw error เพื่อให้ test ดำเนินต่อไป
  }
  // await page.bringToFront();
  // await adminPage.page.waitForTimeout(30000);
});
