import { test, expect, type Page } from '@playwright/test';
import { ConsentPage } from '../pages/VirtualHospitalOld';
import locs from '../locs/consent/index';
import i18n, { Lang } from '../i18n';

let page: Page;
let patientPage: ConsentPage;
let adminPage: Page;
const lang = (process.env.LANG === 'th') ? 'th' : 'en';

test.beforeEach(async ({ context }) => {
  page = await context.newPage();
  patientPage = new ConsentPage(page);
  await page.goto(process.env.URL_USER!);
});

test.afterEach(async () => {
  await page.close();
});

test('TC_CNS_SVH_001 : Verify that the user can proceed after checking the consent checkbox and clicking the "next" button.',{tag: '@oldVersion'}, async ({ context }) => {
  await patientPage.isClickAccept();
  await patientPage.page.waitForTimeout(5000);
  await patientPage.isClickAccept();
  await patientPage.isInputForm();
  await patientPage.isClickSubmit();
  await patientPage.isClickConfirm();

  adminPage = await context.newPage();
  const loginBackofficePage = new LoginBackofficePage(adminPage);
  await adminPage.goto(process.env.URL_ADMIN!);
  await adminPage.isLoginBackOffice();
  // await page.waitForTimeout(5000);
});
