import { sendSimpleTeamsMessage } from "./sendToTeams";
import * as fs from 'fs';

const webhookUrl =
  "https://bdmsgroup.webhook.office.com/webhookb2/77089b03-86ac-4283-9a54-3f3880bc354a@325c40be-6d2b-4006-b2c5-078947c856d2/IncomingWebhook/3731ec524ce14304a16e1917b35cde3c/d00d6fcd-747f-47b7-9fe1-55a34064c1fe/V23Dlbo9fxjdS4xkLrjQok5ZjXQT36qmjKVFIclrsvMxw1";
  // "https://bdmsgroup.webhook.office.com/webhookb2/37c6a24a-aa0e-4d45-adea-fa8400cbf5e4@325c40be-6d2b-4006-b2c5-078947c856d2/IncomingWebhook/51643aae01dc4242948769a5ac358336/d00d6fcd-747f-47b7-9fe1-55a34064c1fe/V2Q0mIuRhdLMJaYoFje039Zy4f7SIa-jgMbafbwv1bBKs1";

//fuction to clean error message
function cleanErrorMessage(errorMessage: string): string {
  if (!errorMessage) return 'Unknown error';

  let cleaned = errorMessage;

  // Delete ANSI escape sequences (color codes) and other unwanted characters
  cleaned = cleaned.replace(/\x1b\[[0-9;]*[mGKHF]/g, '');
  cleaned = cleaned.replace(/\[[0-9;]*m/g, '');

  // delete characters
  cleaned = cleaned.replace(/\[2m/g, '');
  cleaned = cleaned.replace(/\[22m/g, '');
  cleaned = cleaned.replace(/\[31m/g, '');
  cleaned = cleaned.replace(/\[39m/g, '');

  // add space after expect(locator).
  cleaned = cleaned.replace(/expect\(locator\)\./g, 'expect(locator).');

  // delete whitespace multiple
  cleaned = cleaned.replace(/\s+/g, ' ').trim();

  // Cut message to 250 characters
  if (cleaned.length > 250) {
    cleaned = cleaned.substring(0, 250) + '...';
  }

  return cleaned;
}

// Read and parse results.json
function readTestResults() {
  try {
    const resultsPath = 'test-results/results.json';

    // Check if the file exists
    if (!fs.existsSync(resultsPath)) {
      console.log('⚠️ The file results.json was not found:', resultsPath);

      // Try to find the file in other locations
      const alternativePaths = [
        'results.json',
        './results.json',
        'test-results.json',
        'playwright-report/results.json'
      ];

      console.log('🔍 Searching for file in other locations...');
      for (const altPath of alternativePaths) {
        if (fs.existsSync(altPath)) {
          console.log(`✅ Found file at: ${altPath}`);
          // Continue processing with found file
          const rawData = fs.readFileSync(altPath, 'utf8');
          const testResults = JSON.parse(rawData);
          // Process the found test results (inline processing)
          let passed = 0, failed = 0, skipped = 0, total = 0;
          let totalDuration = 0;
          const failedTests: Array<{title: string, file: string, error: string}> = [];

          if (testResults.suites) {
            testResults.suites.forEach((suite: any) => {
              if (suite.specs) {
                suite.specs.forEach((spec: any) => {
                  const testTitle = spec.title || 'Unknown test';
                  const fileName = suite.title || suite.file || 'Unknown file';

                  if (spec.tests && spec.tests.length > 0) {
                    spec.tests.forEach((test: any) => {
                      total++;

                      if (test.results && test.results.length > 0) {
                        const result = test.results[0];
                        totalDuration += result.duration || 0;

                        switch (result.status) {
                          case 'passed':
                            passed++;
                            break;
                          case 'failed':
                            failed++;
                            const rawError = result.error?.message || result.error?.stack || 'Unknown error';
                            const cleanedError = cleanErrorMessage(rawError);

                            failedTests.push({
                              title: testTitle,
                              file: fileName.replace('tests/', ''),
                              error: cleanedError
                            });
                            break;
                          case 'skipped':
                            skipped++;
                            break;
                        }
                      }
                    });
                  }
                });
              }
            });
          }

          return {
            total,
            passed,
            failed,
            skipped,
            duration: Math.round(totalDuration / 1000),
            failedTests
          };
        } else {
          console.log(`❌ Not found at: ${altPath}`);
        }
      }

      // Show files in current directory
      console.log('📁 Files in current directory:');
      try {
        const files = fs.readdirSync('.');
        files.forEach(file => console.log(`  - ${file}`));

        // Also check test-results directory if it exists
        if (fs.existsSync('test-results')) {
          console.log('📁 Files in test-results directory:');
          const testResultsFiles = fs.readdirSync('test-results');
          testResultsFiles.forEach(file => console.log(`  - test-results/${file}`));
        }
      } catch (e) {
        console.log('Cannot read directory');
      }

      return null;
    }

    // console.log('📖 Reading data from the results.json file...:', resultsPath);

    // Reading JSON file
    const rawData = fs.readFileSync(resultsPath, 'utf8');
    const testResults = JSON.parse(rawData);

    // Debug: Show the structure of testResults.suites for debug
    // console.log('🔍 Debug - Structure of testResults.suites:');
    // if (testResults.suites && testResults.suites.length > 0) {
    //   console.log('Suite 0:', JSON.stringify(testResults.suites[0], null, 2));
    // }

    let passed = 0;
    let failed = 0;
    let timedOut = 0;
    let skipped = 0;
    let total = 0;
    let totalDuration = 0;
    const failedTests: Array<{title: string, file: string, error: string}> = [];

    // loop through test suites
    if (testResults.suites) {
      testResults.suites.forEach((suite: any) => {
        if (suite.specs) {
          suite.specs.forEach((spec: any) => {
            // spec.title is the test case title
            const testTitle = spec.title || 'Unknown test';
            const fileName = suite.title || suite.file || 'Unknown file';

            if (spec.tests && spec.tests.length > 0) {
              spec.tests.forEach((test: any) => {
                total++;

                if (test.results && test.results.length > 0) {
                  const result = test.results[0];
                  totalDuration += result.duration || 0;

                  switch (result.status) {
                    case 'passed':
                      passed++;
                      break;
                    case 'failed':
                      failed++;
                      // call function to clean error message
                      const rawError = result.error?.message || result.error?.stack || 'Unknown error';
                      const cleanedError = cleanErrorMessage(rawError);

                      failedTests.push({
                        title: testTitle,
                        file: fileName.replace('tests/', ''), // ลบ prefix tests/ ออก
                        error: cleanedError
                      });
                      break;
                    case 'timedOut':
                      timedOut++;
                      const rawErrorTimedOut = result.error?.message || result.error?.stack || 'Unknown error';
                      const cleanedErrorTimedOut = cleanErrorMessage(rawErrorTimedOut);

                      failedTests.push({
                        title: testTitle,
                        file: fileName.replace('tests/', ''), // ลบ prefix tests/ ออก
                        error: cleanedErrorTimedOut
                      });
                      break;
                    case 'skipped':
                      skipped++;
                      break;
                  }
                }
              });
            }
          });
        }
      });
    }

    const results = {
      total,
      passed,
      failed,
      timedOut,
      skipped,
      duration: Math.round(totalDuration / 1000), // convert to seconds
      failedTests
    };

    // console.log('✅ Results parsed:', results);
    return results;

  } catch (error) {
    console.error('❌ Error parsing reading to results.json:', error);
    return null;
  }
}

// Read information from results.json
// console.log('🔍 Read information from results.json...');
const actualResults = readTestResults();

// ใช้ข้อมูลจริง หรือข้อมูล fallback ถ้าอ่านไฟล์ไม่ได้
const testResults = actualResults || {
  total: 0,
  passed: 0,
  failed: 0,
  timedOut: 0,
  skipped: 0,
  duration: 0,
  failedTests: []
};

// Create teams message
let message = `🔔 **Samitivej Virtual Hospital - Test Summary**\n\n`;
message += `📄 **Report:** [View Detailed Report](https://digital-health-venture.github.io/Automation-Test--Samitivej-Virtual-Hospital/)\n\n`;
message += `📊 **Test Result Summary**\n`;
message += `• 🧪 Total: **${testResults.total}**\n`;
message += `• ✅ Passed: **${testResults.passed}**\n`;
message += `• ❌ Failed: **${testResults.failed}**\n`;
// Remove timedOut line since it's not in our data structure
message += `• ⏭️ Skipped: **${testResults.skipped}**\n`;
message += `• ⏱️ Duration: **${testResults.duration} s**\n\n`;

// Additional information for the failed test (if any)
if (testResults.failed > 0 && testResults.failedTests.length > 0) {
  message += `---\n`;
  message += `❌ **Failed Tests Details**\n\n`;

  const maxFailedToShow = testResults.failedTests.length;
  const failedToShow = testResults.failedTests.slice(0, maxFailedToShow);

  failedToShow.forEach((test) => {
    message += `---\n`;
    message += `• 🧪 **Title:** ${test.title.substring(0, 110)}${test.error.length > 110 ? '...' : ''}\n\n`;
    message += `• 📄 **File:** ${test.file}\n\n`;
    message += `• 💥 **Error:**\n\`\`\`\n${test.error.substring(0, 200)}${test.error.length > 200 ? '...' : ''}\n\`\`\`\n`;
  });

  if (testResults.failedTests.length > maxFailedToShow) {
    message += `... และอีก ${testResults.failedTests.length - maxFailedToShow} tests ที่ fail\n\n`;
  }
} else if (testResults.total > 0 && testResults.failed === 0) {
  message += `🎉 **All tests passed!**\n\n`;
} else {
  message += `❌ **No tests were executed.**\n\n`;
}

// Overall Status
if (actualResults === null) {
  message += `⚠️ **Warning:** Unable to read the results.json file. Please check the report link above.`;
} else if (testResults.failed === 0 && testResults.total > 0) {
  message += `✅ **Status:** All tests completed successfully!`;
} else if (testResults.failed > 0) {
  message += `⚠️ **Status:**  Test(s) failed. Please check the details above.`;
}


// Send Teams notification
async function sendNotification() {
  try {
    // console.log('📤 Sending Teams notification...');
    // console.log('📝 Message preview:', message.substring(0, 200) + '...');

    await sendSimpleTeamsMessage(webhookUrl, message);
    // console.log('✅ Sending to teams notification complete!');
  } catch (error) {
    console.error('❌ Error - Failed to send to Teams notification:', error);
  }
}

// call function to send notification
sendNotification();
