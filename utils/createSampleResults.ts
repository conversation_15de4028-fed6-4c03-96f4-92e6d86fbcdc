import * as fs from 'fs';

// สร้างไฟล์ results.json ตัวอย่างเพื่อทดสอบ
function createSampleResults() {
  const sampleResults = {
    "config": {
      "configFile": "/path/to/playwright.config.ts",
      "rootDir": "/path/to/project"
    },
    "suites": [
      {
        "title": "tests/register.spec.ts",
        "file": "tests/register.spec.ts",
        "specs": [
          {
            "title": "TC_REG_SVH_001 : Verify successful registration with valid input.",
            "ok": true,
            "tests": [
              {
                "timeout": 30000,
                "results": [
                  {
                    "status": "passed",
                    "duration": 5234
                  }
                ]
              }
            ]
          },
          {
            "title": "TC_REG_SVH_002 : Verify that an error is displayed when the first name is empty.",
            "ok": false,
            "tests": [
              {
                "timeout": 30000,
                "results": [
                  {
                    "status": "failed",
                    "duration": 8567,
                    "error": {
                      "message": "Error: [31mTimed out 5000ms waiting for [39m[2mexpect([22m[31mlocator[39m[2m).[22m[31mtoBeVisible[39m[2m().[22m"
                    }
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "title": "tests/inCall.spec.ts",
        "file": "tests/inCall.spec.ts",
        "specs": [
          {
            "title": "TC_IN_CALL_SVH_003 : Verify that the user can turn on the camera and show video.",
            "ok": true,
            "tests": [
              {
                "timeout": 30000,
                "results": [
                  {
                    "status": "passed",
                    "duration": 12345
                  }
                ]
              }
            ]
          },
          {
            "title": "TC_IN_CALL_SVH_004 : Verify the display of the End Call button.",
            "ok": false,
            "tests": [
              {
                "timeout": 30000,
                "results": [
                  {
                    "status": "skipped",
                    "duration": 0
                  }
                ]
              }
            ]
          }
        ]
      }
    ],
    "errors": [],
    "stats": {
      "expected": 2,
      "unexpected": 1,
      "skipped": 1,
      "ok": false
    }
  };

  // สร้างโฟลเดอร์ test-results ถ้ายังไม่มี
  if (!fs.existsSync('test-results')) {
    fs.mkdirSync('test-results', { recursive: true });
  }

  // เขียนไฟล์ results.json
  fs.writeFileSync('test-results/results.json', JSON.stringify(sampleResults, null, 2));
  console.log('✅ สร้างไฟล์ test-results/results.json ตัวอย่างเรียบร้อย');
  
  // แสดงข้อมูลที่สร้าง
  console.log('📊 ข้อมูลที่สร้าง:');
  console.log('- Total suites:', sampleResults.suites.length);
  console.log('- Total specs:', sampleResults.suites.reduce((acc, suite) => acc + suite.specs.length, 0));
}

// เรียกใช้ฟังก์ชัน
createSampleResults();
