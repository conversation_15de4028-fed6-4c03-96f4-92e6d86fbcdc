import { Page } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';

export class FileUploadHelper {
  /**
   * ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
   */
  static fileExists(filePath: string): boolean {
    return fs.existsSync(filePath);
  }

  /**
   * สร้างไฟล์รูปภาพตัวอย่างถ้ายังไม่มี
   */
  static async createSampleImages(): Promise<void> {
    const imgDir = './data-test/img';

    // สร้างโฟลเดอร์ถ้ายังไม่มี
    if (!fs.existsSync(imgDir)) {
      fs.mkdirSync(imgDir, { recursive: true });
    }

    // Base64 ของรูปภาพ PNG ขนาดเล็ก
    const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    const imageBuffer = Buffer.from(base64Image, 'base64');

    const images = [
      { name: 'sample.png', buffer: imageBuffer },
      { name: 'test-image.jpg', buffer: imageBuffer },
      { name: 'upload-test.jpeg', buffer: imageBuffer },
      { name: 'large-image.png', buffer: imageBuffer }
    ];

    images.forEach(img => {
      const filePath = path.join(imgDir, img.name);
      if (!fs.existsSync(filePath)) {
        fs.writeFileSync(filePath, img.buffer);
        console.log(`✅ Created sample image: ${filePath}`);
      }
    });
  }

  /**
   * รายการไฟล์รูปภาพที่ใช้ทดสอบ
   */
  static getTestImages(): Array<{name: string, path: string, type: string}> {
    return [
      { name: 'PNG Image', path: './data-test/img/sample.png', type: 'image/png' },
      { name: 'JPG Image', path: './data-test/img/test-image.jpg', type: 'image/jpeg' },
      { name: 'JPEG Image', path: './data-test/img/upload-test.jpeg', type: 'image/jpeg' },
      { name: 'Large PNG', path: './data-test/img/large-image.png', type: 'image/png' }
    ];
  }

  /**
   * ทดสอบการอัพโหลดไฟล์โดยใช้หลายวิธี
   */
  static async uploadFileWithFallback(page: Page, filePath: string): Promise<boolean> {
    try {
      console.log(`📎 Attempting to upload: ${filePath}`);

      // ตรวจสอบว่าไฟล์มีอยู่
      if (!this.fileExists(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // วิธีที่ 1: หา input[type="file"] ทั่วไป
      try {
        const fileInput = page.locator('input[type="file"]').first();
        await fileInput.waitFor({ state: 'attached', timeout: 3000 });
        await fileInput.setInputFiles(filePath);
        console.log('✅ Upload successful (method 1)');
        return true;
      } catch (e) {
        console.log('⚠️ Method 1 failed, trying method 2...');
      }

      // วิธีที่ 2: หา input ที่ซ่อนอยู่
      try {
        const hiddenInput = page.locator('input[type="file"][style*="display: none"], input[type="file"][hidden]').first();
        await hiddenInput.waitFor({ state: 'attached', timeout: 3000 });
        await hiddenInput.setInputFiles(filePath);
        console.log('✅ Upload successful (method 2)');
        return true;
      } catch (e) {
        console.log('⚠️ Method 2 failed, trying method 3...');
      }

      // วิธีที่ 3: หา input ที่มี accept attribute
      try {
        const acceptInput = page.locator('input[accept*="image"], input[accept*="file"]').first();
        await acceptInput.waitFor({ state: 'attached', timeout: 3000 });
        await acceptInput.setInputFiles(filePath);
        console.log('✅ Upload successful (method 3)');
        return true;
      } catch (e) {
        console.log('❌ All upload methods failed');
        return false;
      }

    } catch (error) {
      console.error('❌ Upload error:', error);
      return false;
    }
  }

  /**
   * ตรวจสอบว่าไฟล์ถูกอัพโหลดสำเร็จหรือไม่
   */
  static async verifyFileUpload(page: Page, fileName: string, timeout: number = 5000): Promise<boolean> {
    try {
      // หาชื่อไฟล์ใน UI
      const fileElement = page.locator(`text="${fileName}"`).first();
      await fileElement.waitFor({ state: 'visible', timeout });
      console.log(`✅ File "${fileName}" appears in UI`);
      return true;
    } catch (e) {
      console.log(`⚠️ Could not verify file "${fileName}" in UI`);
      return false;
    }
  }

  /**
   * ดึงชื่อไฟล์จาก path
   */
  static getFileName(filePath: string): string {
    return path.basename(filePath);
  }

  /**
   * ดึงขนาดไฟล์
   */
  static getFileSize(filePath: string): number {
    try {
      const stats = fs.statSync(filePath);
      return stats.size;
    } catch (e) {
      return 0;
    }
  }
}
