{"name": "automation-test--samitivej-virtual-hospital", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "npx playwright test", "test:all": "ENV=uat LANG=th npx playwright test", "test:dev": "ENV=dev npx playwright test", "test:uat": "ENV=uat npx playwright test --headed", "test:incall": "ENV=uat LANG=th npx playwright test ./tests/register.spec.ts --headed", "test:incall-headless": "ENV=uat LANG=th npx playwright test ./tests/register.spec.ts --timeout=60000", "test:prod": "ENV=prod npx playwright test", "test:lang-th": "LANG=th npx playwright test", "test:lang-en": "LANG=en npx playwright test", "test:prod-th": "ENV=prod LANG=th npx playwright test", "test:codegen": "npx playwright codegen", "test:tag": "ENV=sit LANG=th npx playwright test ./tests-end-to-end/virtualHospitalOld.spec.ts --headed"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.52.0", "@types/node": "^22.15.30", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"dotenv": "^16.5.0", "mysql2": "^3.14.1"}}