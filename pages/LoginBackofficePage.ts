import { Page, Locator } from '@playwright/test';
import locators from '../locs/loginBackoffice/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class LoginBackofficePage {
  readonly t = i18n[lang];
  readonly page: Page;

  constructor(page: Page) {
    this.t = i18n[lang];
    this.page = page;
  }

  async fillName(name: string) {
    //  await this.page.waitForSelector( `//input[@placeholder='${this.t.dashboard.timeAtWork}']`);
    await this.page.waitForSelector(locators.nameInput);
    await this.page.click(locators.nameInput);
    await this.page.fill(locators.nameInput, name);
  }

  async fillPassword(password: string) {
    await this.page.waitForSelector(locators.passwordInput);
    await this.page.click(locators.passwordInput);
    await this.page.fill(locators.passwordInput, password);
  }

  async clickLogin() {
    await this.page.waitForSelector(locators.loginButton);
    await this.page.click(locators.loginButton);
  }

  async loginBackoffice(name: string, password: string) {
    await this.fillName(name);
    await this.fillPassword(password);
    await this.clickLogin();
  }
}
