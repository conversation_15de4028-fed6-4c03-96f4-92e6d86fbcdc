import { Page, Locator } from '@playwright/test';
import locators from '../locs/consent/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class ConsentPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isClickCheckbox() {
    await this.page.waitForSelector(locators.consentCheckbox);
    await this.page.click(locators.consentCheckbox);
  }

  async isConfirm() {
    await this.page.waitForSelector(locators.confirmButton);
    await this.page.click(locators.confirmButton);
  }

  async acceptConsent() {
    await this.isClickCheckbox();
    await this.isConfirm();
  }

}