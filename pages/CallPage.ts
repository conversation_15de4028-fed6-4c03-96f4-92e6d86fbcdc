import { Page, Locator } from '@playwright/test';
import locators from '../locs/call/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class CallPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isStartCall() {
    await this.page.waitForSelector(locators.startCallButton);
    await this.page.click(locators.startCallButton);
  }

  async isEndCall() {
    await this.page.waitForSelector(locators.backButton);
    await this.page.click(locators.backButton);
  }

  async isClickCall() {
    await this.page.waitForSelector(locators.callButton);
    await this.page.click(locators.callButton);
  }
}