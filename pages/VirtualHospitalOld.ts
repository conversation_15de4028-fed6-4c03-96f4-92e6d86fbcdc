import { Page, Locator } from '@playwright/test';
import locators from '../locs/virtualHospitalOld/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class ConsentPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isClickAccept() {
    await this.page.waitForSelector(locators.btnAccept);
    await this.page.click(locators.btnAccept);
  }

  async isInputForm() {
    await this.page.waitForSelector(locators.inputFirstName);
    await this.page.click(locators.inputFirstName);
    await this.page.fill(locators.inputFirstName, "Liam");

    await this.page.waitForSelector(locators.inputLastName);
    await this.page.click(locators.inputLastName);
    await this.page.fill(locators.inputLastName, "CarterTest");

    await this.page.waitForSelector(locators.inputTel);
    await this.page.click(locators.inputTel);
    await this.page.fill(locators.inputTel, "0995033562");
  }

  async isClickSubmit() {
    await this.page.waitForSelector(locators.btnSubmit);
    await this.page.click(locators.btnSubmit);
  }

  async isRegister() {
    await this.isClickAccept();
    await this.isInputForm();
    await this.isClickSubmit();
  }

  async isClickConfirm() {
    await this.page.waitForSelector(locators.btnConfirm);
    await this.page.click(locators.btnConfirm);
  }

  async isConfirmService() {
    await this.isRegister();
    await this.isClickConfirm();
  }

  async isLoginBackOffice() {
    await this.page.waitForSelector(locators.inputUsername);
    await this.page.click(locators.inputUsername);
    await this.page.fill(locators.inputUsername, "adminuat");

    await this.page.waitForSelector(locators.inputPassword);
    await this.page.click(locators.inputPassword);
    await this.page.fill(locators.inputPassword, "temp1234*");

    await this.page.waitForSelector(locators.btnLogin);
    await this.page.click(locators.btnLogin);
  }

}