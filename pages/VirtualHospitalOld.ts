import { Page, Locator } from '@playwright/test';
import locators from '../locs/virtualHospitalOld/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class ConsentPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isClickAccept() {
    await this.page.waitForSelector(locators.btnAccept);
    await this.page.click(locators.btnAccept);
  }

  async isInputForm() {
    await this.page.waitForSelector(locators.inputFirstName);
    await this.page.click(locators.inputFirstName);
    await this.page.fill(locators.inputFirstName, "Liam");

    await this.page.waitForSelector(locators.inputLastName);
    await this.page.click(locators.inputLastName);
    await this.page.fill(locators.inputLastName, "CarterTest");

    await this.page.waitForSelector(locators.inputTel);
    await this.page.click(locators.inputTel);
    await this.page.fill(locators.inputTel, "0995033562");
  }

  async isClickSubmit() {
    await this.page.waitForSelector(locators.btnSubmit);
    await this.page.click(locators.btnSubmit);
  }

  async isRegister() {
    await this.isClickAccept();
    await this.isInputForm();
    await this.isClickSubmit();
  }

  async isClickConfirm() {
    await this.page.waitForSelector(locators.btnConfirm);
    await this.page.click(locators.btnConfirm);
  }

  async isConfirmService() {
    await this.isRegister();
    await this.isClickConfirm();
  }

  async isLoginBackOffice() {
    await this.page.waitForSelector(locators.inputUsername);
    await this.page.click(locators.inputUsername);
    await this.page.fill(locators.inputUsername, "adminuat");

    await this.page.waitForSelector(locators.inputPassword);
    await this.page.click(locators.inputPassword);
    await this.page.fill(locators.inputPassword, "temp1234*");

    await this.page.waitForSelector(locators.btnLogin);
    await this.page.click(locators.btnLogin);
  }

  async isStartCall() {
    await this.page.waitForSelector(locators.btnStartCall);
    await this.page.click(locators.btnStartCall);
  }

  async isLeaveCall() {
    await this.page.waitForSelector(locators.btnLeaveCall);
    await this.page.click(locators.btnLeaveCall);
  }

  // เพิ่ม method สำหรับตรวจสอบสถานะ call
  async isCallPageActive(): Promise<boolean> {
    try {
      const url = this.page.url();
      return url.includes('call') || url.includes('pre-call') || url.includes('waiting');
    } catch (error) {
      console.log('⚠️ Error checking call page status:', error);
      return false;
    }
  }

  async waitForCallStable(maxWaitTime: number = 30000) {
    const startTime = Date.now();
    let lastUrl = '';
    let urlChangeCount = 0;

    console.log(`🔄 Starting call stability check (max wait: ${maxWaitTime}ms)`);

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const currentUrl = this.page.url();

        // ถ้า URL เปลี่ยน ให้ log และนับ
        if (currentUrl !== lastUrl) {
          urlChangeCount++;
          console.log(`📍 URL change #${urlChangeCount}: ${lastUrl} -> ${currentUrl}`);

          // ถ้า URL เปลี่ยนไปเป็นหน้าอื่นที่ไม่ใช่ call-related
          if (!await this.isCallPageActive()) {
            console.log(`❌ Page navigated away from call: ${currentUrl}`);
            await this.debugPageState();
            return false;
          }
        }

        // ถ้า URL เสถียรแล้ว (ไม่เปลี่ยนใน 5 วินาที)
        if (currentUrl === lastUrl && lastUrl !== '') {
          console.log(`⏳ URL stable for check, waiting 5s more: ${currentUrl}`);
          await this.page.waitForTimeout(5000);

          if (this.page.url() === currentUrl) {
            console.log(`✅ Call page is stable: ${currentUrl}`);
            return true;
          }
        }

        lastUrl = currentUrl;
        await this.page.waitForTimeout(2000);

      } catch (error) {
        console.log('⚠️ Error during call stability check:', error);
        await this.debugPageState();
        return false;
      }
    }

    console.log('⚠️ Call stability check timed out');
    await this.debugPageState();
    return false;
  }

  // เพิ่ม method สำหรับ debug สถานะ page
  async debugPageState() {
    try {
      console.log('🔍 === DEBUG PAGE STATE ===');
      console.log('📍 Current URL:', this.page.url());
      console.log('📄 Page title:', await this.page.title());

      // ตรวจสอบว่ามี error message หรือไม่
      const errorElements = await this.page.locator('text=/error|Error|disconnect|Disconnect|leave|Leave/i').all();
      if (errorElements.length > 0) {
        console.log('⚠️ Found potential error/disconnect messages:');
        for (let i = 0; i < Math.min(errorElements.length, 3); i++) {
          const text = await errorElements[i].textContent();
          console.log(`   - ${text}`);
        }
      }

      // ตรวจสอบว่ามี leave button หรือไม่
      const leaveButton = this.page.locator(locators.btnLeaveCall);
      const isLeaveVisible = await leaveButton.isVisible().catch(() => false);
      console.log('🚪 Leave button visible:', isLeaveVisible);

      console.log('🔍 === END DEBUG ===');
    } catch (error) {
      console.log('⚠️ Error during debug:', error);
    }
  }

}