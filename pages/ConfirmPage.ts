import { Page, Locator } from '@playwright/test';
import locators from '../locs/confirm/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class ConfirmPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isClickConfirm() {
    await this.page.waitForSelector(locators.btnConfirm);
    await this.page.click(locators.btnConfirm);
  }

  async isClickBack() {
    await this.page.waitForSelector(locators.backButton);
    await this.page.click(locators.backButton);
  }
}