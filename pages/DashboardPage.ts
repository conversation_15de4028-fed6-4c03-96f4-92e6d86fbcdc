import { Page, Locator } from '@playwright/test';
import locators from '../locs/dashboard/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class DashboardPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isStartCall() {
    await this.page.waitForSelector(locators.startCallButton);
    await this.page.click(locators.startCallButton);
  }

  async isClickDashboard() {
    await this.page.waitForSelector(locators.menuDashboard);
    await this.page.click(locators.menuDashboard);
  }

  async isClickInfoButton(fullName: string) {
    // ดึง card ทั้งหมดที่เป็น div.col-md-3
    await this.page.waitForSelector("//div[@class='col-md-3']");
    const cards = await this.page.locator("//div[@class='col-md-3']").all();

    for (let i = 0; i < cards.length; i++) {
      const nameEl = await this.page.locator(`(//div[@class='col-md-3'])[${i + 1}]//div[@class='name_customer']`).textContent();
      if (nameEl?.trim() === fullName) {
        await this.page.locator(`(//div[@class='col-md-3'])[${i + 1}]//a[normalize-space()='Information']`).click();
        return;
      }
    }
  }

  async isClickWaitingNurse() {
    await this.page.waitForSelector(locators.menuWaitingNurse);
    await this.page.click(locators.menuWaitingNurse);
  }

  async isClickNurseRoom() {
    await this.page.waitForSelector(locators.menuNurseRoom);
    await this.page.click(locators.menuNurseRoom);
  }

  async isEndCallQueue() {
    await this.page.waitForSelector(locators.endCallQueueButton);
    await this.page.click(locators.endCallQueueButton);
  }

  async isConfirmEndCallQueue() {
    await this.page.waitForSelector(locators.endCallQueueConfirmButton);
    await this.page.click(locators.endCallQueueConfirmButton);
  }

  async isClickOkButton() {
    await this.page.waitForSelector(locators.confirmOkButton);
    await this.page.click(locators.confirmOkButton);
  }
}