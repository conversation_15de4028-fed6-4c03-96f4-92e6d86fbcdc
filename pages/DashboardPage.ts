import { Page, Locator } from '@playwright/test';
import locators from '../locs/dashboard/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class DashboardPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isStartCall() {
    try {
      await this.page.waitForSelector(locators.startCallButton, { timeout: 10000 });
      await this.page.click(locators.startCallButton);
      // รอให้ call session เริ่มต้นเสร็จสิ้น
      await this.page.waitForTimeout(2000);
    } catch (error) {
      console.log('Error starting call:', error);
      throw error;
    }
  }

  async isClickDashboard() {
    await this.page.waitForSelector(locators.menuDashboard);
    await this.page.click(locators.menuDashboard);
  }

  async isClickInfoButton(fullName: string) {
    // ดึง card ทั้งหมดที่เป็น div.col-md-3
    await this.page.waitForSelector("//div[@class='col-md-3']");
    const cards = await this.page.locator("//div[@class='col-md-3']").all();

    for (let i = 0; i < cards.length; i++) {
      const nameEl = await this.page.locator(`(//div[@class='col-md-3'])[${i + 1}]//div[@class='name_customer']`).textContent();
      if (nameEl?.trim() === fullName) {
        await this.page.locator(`(//div[@class='col-md-3'])[${i + 1}]//a[normalize-space()='Information']`).click();
        return;
      }
    }
  }

  async isClickWaitingNurse() {
    await this.page.waitForSelector(locators.menuWaitingNurse);
    await this.page.click(locators.menuWaitingNurse);
  }

  async isClickNurseRoom() {
    await this.page.waitForSelector(locators.menuNurseRoom);
    await this.page.click(locators.menuNurseRoom);
  }

  async isEndCallQueue() {
    try {
      await this.page.waitForSelector(locators.endCallQueueButton, { timeout: 10000 });
      await this.page.click(locators.endCallQueueButton);
      console.log('✅ End call queue button clicked');
    } catch (error) {
      console.log('⚠️ Error ending call queue:', error);
      throw error;
    }
  }

  async isConfirmEndCallQueue() {
    try {
      await this.page.waitForSelector(locators.endCallQueueConfirmButton, { timeout: 10000 });
      await this.page.click(locators.endCallQueueConfirmButton);
      console.log('✅ Confirm end call queue clicked');
    } catch (error) {
      console.log('⚠️ Error confirming end call queue:', error);
      throw error;
    }
  }

  async isClickOkButton() {
    try {
      await this.page.waitForSelector(locators.confirmOkButton, { timeout: 10000 });
      await this.page.click(locators.confirmOkButton);
      console.log('✅ OK button clicked');
    } catch (error) {
      console.log('⚠️ Error clicking OK button:', error);
      throw error;
    }
  }

  // เพิ่ม method สำหรับตรวจสอบสถานะ call
  async isCallActive(fullName: string): Promise<boolean> {
    try {
      await this.page.waitForSelector("//div[@class='col-md-3']", { timeout: 5000 });
      const cards = await this.page.locator("//div[@class='col-md-3']").all();

      for (let i = 0; i < cards.length; i++) {
        const nameEl = await this.page.locator(`(//div[@class='col-md-3'])[${i + 1}]//div[@class='name_customer']`).textContent();
        if (nameEl?.trim() === fullName) {
          // ตรวจสอบว่ามี Start Call button หรือไม่
          const startCallButton = this.page.locator(`(//div[@class='col-md-3'])[${i + 1}]//a[normalize-space()='Start Call']`);
          const isVisible = await startCallButton.isVisible();
          return isVisible; // ถ้ามี Start Call button แสดงว่า call ยังไม่ active
        }
      }
      return false;
    } catch (error) {
      console.log('⚠️ Error checking call status:', error);
      return false;
    }
  }

  // เพิ่ม method สำหรับ cleanup call session อย่างปลอดภัย
  async safeEndCallSession(fullName: string) {
    try {
      console.log(`🔄 Starting safe end call session for: ${fullName}`);

      // ตรวจสอบว่า call ยัง active อยู่หรือไม่
      const isActive = await this.isCallActive(fullName);
      if (!isActive) {
        console.log('ℹ️ Call is not active, skipping end call process');
        return;
      }

      await this.isClickDashboard();
      await this.page.waitForTimeout(1000);

      await this.isClickNurseRoom();
      await this.page.waitForTimeout(1000);

      await this.isClickInfoButton(fullName);
      await this.page.waitForTimeout(1000);

      await this.isEndCallQueue();
      await this.page.waitForTimeout(2000);

      await this.isConfirmEndCallQueue();
      await this.page.waitForTimeout(2000);

      await this.isClickOkButton();
      await this.page.waitForTimeout(1000);

      console.log('✅ Call session ended successfully');
    } catch (error) {
      console.log('⚠️ Error in safe end call session:', error);
      // ไม่ throw error เพื่อไม่ให้ test fail
    }
  }
}