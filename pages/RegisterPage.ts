import { Page, Locator } from '@playwright/test';
import locators from '../locs/register/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class RegisterPage {
  readonly t = i18n[lang];
  readonly page: Page;

  constructor(page: Page) {
    this.t = i18n[lang];
    this.page = page;
  }

  async fillFirstName(firstName: string) {
    await this.page.waitForSelector(locators.firstNameInput);
    await this.page.click(locators.firstNameInput);
    await this.page.fill(locators.firstNameInput, firstName);
  }

  async fillLastName(lastName: string) {
    await this.page.waitForSelector(locators.lastNameInput);
    await this.page.click(locators.lastNameInput);
    await this.page.fill(locators.lastNameInput, lastName);
  }

  async fillTel(mobileNo: string) {
    await this.page.waitForSelector(locators.telInput);
    await this.page.click(locators.telInput);
    await this.page.fill(locators.telInput, mobileNo);
  }

  async clickSubmit() {
    await this.page.waitForSelector(locators.submitButton);
    await this.page.click(locators.submitButton);
  }

  async isClearFill(fillName: string) {
    if(fillName === 'firstName'){
      await this.page.fill(locators.firstNameInput, "");
    } else if(fillName === 'lastName'){
      await this.page.fill(locators.lastNameInput, "");
    } else if(fillName === 'tel'){
      await this.page.fill(locators.telInput, "");
    }
  }

  async submitRegister(firstName: string, lastName: string, mobileNo: string) {
    if(firstName === ''){
      await this.page.waitForSelector(locators.firstNameInput);
      await this.page.click(locators.firstNameInput);
      await this.page.fill(locators.firstNameInput, "firstName");
      await this.isClearFill('firstName');
      await this.fillLastName(lastName);
      await this.fillTel(mobileNo);
    } else if(lastName === ''){
      await this.page.waitForSelector(locators.lastNameInput);
      await this.page.click(locators.lastNameInput);
      await this.page.fill(locators.lastNameInput, "lastName");
      await this.isClearFill('lastName');
      await this.fillFirstName(firstName);
      await this.fillTel(mobileNo);
    } else if(mobileNo === ''){
      await this.page.waitForSelector(locators.telInput);
      await this.page.click(locators.telInput);
      await this.page.fill(locators.telInput, "tel");
      await this.isClearFill('tel');
      await this.fillFirstName(firstName);
      await this.fillLastName(lastName);
    } else {
      await this.fillFirstName(firstName);
      await this.fillLastName(lastName);
      await this.fillTel(mobileNo);
      if(mobileNo.length < 10 || mobileNo.length > 10){
        await this.fillTel(mobileNo);
      } else {
        await this.clickSubmit();
      }
    }
  }
}
