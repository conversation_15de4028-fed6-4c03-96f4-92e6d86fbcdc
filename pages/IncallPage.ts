import { Page, Locator, expect } from '@playwright/test';
import locators from '../locs/call/index';
import i18n, { Lang } from '../i18n';
import { FileUploadHelper } from '../utils/fileUploadHelper';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class InCallPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isClickBack() {
    await this.page.waitForSelector(locators.backButton);
    await this.page.click(locators.backButton);
  }

  async isEndCall() {
    await this.page.waitForSelector(locators.endCallButton);
    await this.page.click(locators.endCallButton);
  }

  async isClickMicrophoneOnButton() {
    await this.page.waitForSelector(locators.microphoneOffButton);
    await this.page.click(locators.microphoneOffButton);
  }

  async isClickMicrophoneOffButton() {
    await this.page.waitForSelector(locators.microphoneOnButton);
    await this.page.click(locators.microphoneOnButton);
  }

  async isClickCameraOnButton() {
    await this.page.waitForSelector(locators.cameraOffButton);
    await this.page.click(locators.cameraOffButton);
  }

  async isClickCameraOffButton() {
    await this.page.waitForSelector(locators.cameraOnButton);
    await this.page.click(locators.cameraOnButton);
  }

  async isClickRaiseHandButton() {
    await this.page.waitForSelector(locators.raiseHandButton);
    await this.page.click(locators.raiseHandButton);
  }

  async isClickLowerHandButton() {
    await this.page.waitForSelector(locators.lowerHandButton);
    await this.page.click(locators.lowerHandButton);
  }

  async isClickPresentScreenOnButton() {
    await this.page.waitForSelector(locators.cameraButton);
    await this.page.click(locators.cameraButton);
  }

  async isConfirmLeaveCall() {
    await this.page.waitForSelector(locators.leaveCallButton);
    await this.page.click(locators.leaveCallButton);
  }

  async isSkipLeaveCall() {
    await this.page.waitForSelector(locators.skipButton);
    await this.page.click(locators.skipButton);
  }

  async isClickPeoplePanel() {
    await this.page.waitForSelector(locators.peopleButton);
    await this.page.click(locators.peopleButton);
  }

  async isClickClosePanel() {
    await this.page.waitForSelector(locators.iconClosePanel);
    await this.page.click(locators.iconClosePanel);
  }

  async isClickChatPanel() {
    await this.page.waitForSelector(locators.chatButton);
    await this.page.click(locators.chatButton);
  }

  async isSendMessage(text: string) {
    await this.page.waitForSelector(locators.inputChat);
    await this.page.fill(locators.inputChat, text);
    await this.page.keyboard.press('Enter');
  }

  async isVerifyMessage(text: string) {
    await this.page.waitForSelector(`//div[@aria-label='You said ${text}']`);
    await expect(this.page.locator(`//div[@aria-label='You said ${text}']`)).toHaveText(text);
  }

  async isSendBoxAttachFile(filePath: string) {
    try {
      // ตรวจสอบว่าไฟล์มีอยู่จริง
      const fs = require('fs');
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // รอให้ file input dialog เปิดขึ้น
      await this.page.waitForTimeout(1000);

      // ลองหา input file element หลายวิธี
      let fileInput;

      // วิธีที่ 1: หา input[type="file"] ทั่วไป
      try {
        fileInput = this.page.locator('input[type="file"]').first();
        await fileInput.waitFor({ state: 'attached', timeout: 3000 });
      } catch (e) {
        console.log('⚠️ Method 1 failed, trying method 2...');

        // วิธีที่ 2: หา input ที่ซ่อนอยู่
        try {
          fileInput = this.page.locator('input[type="file"][style*="display: none"], input[type="file"][hidden]').first();
          await fileInput.waitFor({ state: 'attached', timeout: 3000 });
          console.log('✅ Found hidden file input (method 2)');
        } catch (e2) {
          console.log('⚠️ Method 2 failed, trying method 3...');

          // วิธีที่ 3: หา input ใดๆ ที่เกี่ยวข้องกับ file upload
          fileInput = this.page.locator('input[accept*="image"], input[accept*="file"]').first();
          await fileInput.waitFor({ state: 'attached', timeout: 3000 });
          console.log('✅ Found file input with accept attribute (method 3)');
        }
      }

      // อัพโหลดไฟล์
      await fileInput.setInputFiles(filePath);
      // รอให้ไฟล์อัพโหลดเสร็จและแสดงใน UI
      await this.page.waitForTimeout(3000);

      // ตรวจสอบว่ามีการแสดงไฟล์ใน chat หรือไม่ (optional)
      try {
        // หาชื่อไฟล์ใน UI
        const fileName = filePath.split('/').pop() || filePath.split('\\').pop();
        const fileElement = this.page.locator(`text="${fileName}"`).first();
        await fileElement.waitFor({ state: 'visible', timeout: 5000 });
        console.log('✅ File appears in chat UI');
        await this.page.keyboard.press('Enter');
      } catch (e) {
        console.log('⚠️ Could not verify file in UI, but upload may have succeeded');
      }

    } catch (error) {
      console.error('❌ Error in isSendBoxAttachFile:', error);
      throw error;
    }
  }

  async isDeleteMessage(text: string) {
    await this.page.waitForSelector(`//div[@aria-label='You said ${text}']`);
    await this.page.click(`//div[@aria-label='You said ${text}']`);
    await this.page.waitForTimeout(1000);
    await this.page.click(locators.iconAcionMsg);
    await this.page.waitForSelector(locators.deleteMessageButton);
    await this.page.click(locators.deleteMessageButton);
  }

  async isEditMessage(text: string, newText: string) {
    await this.page.waitForSelector(`//div[@aria-label='You said ${text}']`);
    await this.page.click(`//div[@aria-label='You said ${text}']`);
    await this.page.waitForTimeout(1000);
    await this.page.click(locators.iconAcionMsg);
    await this.page.waitForSelector(locators.editMessageButton);
    await this.page.click(locators.editMessageButton);
    await this.page.waitForSelector(locators.textareaEditMessage);
    await this.page.fill(locators.textareaEditMessage, newText);
    await this.page.click(locators.submitEditMessageButton);
    await this.page.waitForTimeout(3000);
  }
}