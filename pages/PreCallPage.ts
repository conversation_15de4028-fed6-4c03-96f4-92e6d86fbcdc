import { Page, Locator } from '@playwright/test';
import locators from '../locs/call/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class PreCallPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isClickBack() {
    await this.page.waitForSelector(locators.backButton);
    await this.page.click(locators.backButton);
  }

  async isCancelCall() {
    await this.page.waitForSelector(locators.cancelCallButton);
    await this.page.click(locators.cancelCallButton);
  }

  async isClickStartCall() {
    await this.page.waitForSelector(locators.startCallButton);
    await this.page.click(locators.startCallButton);
  }

  async isWaitForService() {
    await this.page.waitForSelector(locators.waitForServiceButton);
    await this.page.click(locators.waitForServiceButton);
  }

  async isLeaveMessage() {
    await this.page.waitForSelector(locators.leaveMessageButton);
    await this.page.click(locators.leaveMessageButton);
  }

  async isClickMicrophoneButton() {
    const micButton = this.page.locator(locators.microphoneButton);
    await micButton.waitFor({ state: 'visible', timeout: 5000 });
    await micButton.click();
  }
}