import { Page, Locator } from '@playwright/test';
import locators from '../locs/call/startCall';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class StartCallPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isClickStartCall() {
    await this.page.waitForSelector(locators.startCallButton);
    await this.page.click(locators.startCallButton);
  }

  async isClickCall() {
    await this.page.waitForSelector(locators.callButton);
    await this.page.click(locators.callButton);
  }

  async isClickCameraDropdown() {
    await this.page.waitForSelector(locators.cameraDropdown);
    await this.page.click(locators.cameraDropdown);
  }

  async isClickMicrophoneDropdown() {
    await this.page.waitForSelector(locators.microphoneDropdown);
    await this.page.click(locators.microphoneDropdown);
  }

  async isClickSpeakerDropdown() {
    await this.page.waitForSelector(locators.speakerDropdown);
    await this.page.click(locators.speakerDropdown);
  }

  async isClickCameraOffButton() {
    await this.page.waitForSelector(locators.cameraOffButton);
    await this.page.click(locators.cameraOffButton);
  }

  async isClickMicrophoneOffButton() {
    await this.page.waitForSelector(locators.microphoneOffButton);
    await this.page.click(locators.microphoneOffButton);
  }

  async isClickCameraOnButton() {
    await this.page.waitForSelector(locators.cameraOnButton);
    await this.page.click(locators.cameraOnButton);
  }

  async isClickMicrophoneOnButton() {
    await this.page.waitForSelector(locators.microphoneOnButton);
    await this.page.click(locators.microphoneOnButton);
  }
}