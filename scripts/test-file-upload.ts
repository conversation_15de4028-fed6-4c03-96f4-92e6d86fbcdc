import { FileUploadHelper } from '../utils/fileUploadHelper';

/**
 * Script สำหรับทดสอบ FileUploadHelper
 */
async function testFileUploadHelper() {
  console.log('🧪 Testing FileUploadHelper functionality...\n');

  try {
    // 1. สร้างไฟล์ตัวอย่าง
    console.log('📁 Creating sample images...');
    await FileUploadHelper.createSampleImages();
    console.log('✅ Sample images created\n');

    // 2. ทดสอบการตรวจสอบไฟล์
    console.log('🔍 Testing file existence checks...');
    const testImages = FileUploadHelper.getTestImages();
    
    testImages.forEach(image => {
      const exists = FileUploadHelper.fileExists(image.path);
      const size = FileUploadHelper.getFileSize(image.path);
      const fileName = FileUploadHelper.getFileName(image.path);
      
      console.log(`📄 ${image.name}:`);
      console.log(`   Path: ${image.path}`);
      console.log(`   Exists: ${exists ? '✅' : '❌'}`);
      console.log(`   Size: ${size} bytes`);
      console.log(`   Filename: ${fileName}`);
      console.log(`   Type: ${image.type}\n`);
    });

    // 3. แสดงสรุป
    console.log('📊 Summary:');
    console.log(`   Total test images: ${testImages.length}`);
    console.log(`   Available images: ${testImages.filter(img => FileUploadHelper.fileExists(img.path)).length}`);
    console.log(`   Missing images: ${testImages.filter(img => !FileUploadHelper.fileExists(img.path)).length}`);

    console.log('\n✅ FileUploadHelper test completed successfully!');

  } catch (error) {
    console.error('❌ Error testing FileUploadHelper:', error);
  }
}

// เรียกใช้ฟังก์ชันทดสอบ
testFileUploadHelper();
