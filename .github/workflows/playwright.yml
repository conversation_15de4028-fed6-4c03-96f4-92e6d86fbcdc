name: <PERSON><PERSON>j Virtual Hospital Test

on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

jobs:
  automation-test:
    timeout-minutes: 30
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Install dependencies
        run: npm ci

      - name: Cache Playwright browsers
        uses: actions/cache@v4
        id: playwright-cache
        with:
          path: |
            ~/.cache/ms-playwright/
          key: ${{ runner.os }}-playwright-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-playwright-

      - name: Install Playwright Browsers
        run: npx playwright install --with-deps

      - name: Run Playwright tests
        run: npm run test:tag
        continue-on-error: true
        env:
          # Environment variables สำหรับ headless mode
          DISPLAY: ":99"
          PLAYWRIGHT_BROWSERS_PATH: ~/.cache/ms-playwright
          # ป้องกันการค้างใน CI
          CI: true

      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: test-results
          path: test-results/
          retention-days: 30

      - name: Clean GitHub Pages cache
        if: ${{ github.ref == 'refs/heads/main' && !cancelled() }}
        run: rm -rf playwright-report/.git

      - name: Deploy report to GitHub Pages
        if: ${{ github.ref == 'refs/heads/main' && !cancelled() }}
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: playwright-report

  notify:
    needs: automation-test
    if: always()
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Install dependencies
        run: npm ci

      - name: Download test results
        uses: actions/download-artifact@v4
        with:
          name: test-results
          path: test-results/
        continue-on-error: true

      - name: List downloaded files (debug)
        run: |
          echo "📁 Current directory contents:"
          ls -la
          echo "📁 test-results directory contents:"
          ls -la test-results/ || echo "test-results directory not found"
        continue-on-error: true

      - name: Send Teams notification
        run: npx ts-node ./utils/notifications/teamsNotifier.ts